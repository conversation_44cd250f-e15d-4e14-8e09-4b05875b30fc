import { AxiosResponse } from '@ohos/axios'
import httpRequest from '../httpRequest'

class IndexApi {
  static getList(): Promise<AxiosResponse> {
    return httpRequest({
      url: '/posts',
      method: 'get'
    })
  }

  static getTipList(): Promise<string[]> {
    return Promise.resolve([
      'vivo S30',
      '耳机',
      'vivo X200S',
      'vivo X200 Pro mini',
      'iqoo neo 13',
      'iqoo neo 9s pro +',
      '手机',
      '手表'
    ])
  }
}

export default IndexApi