if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface Home_Params {
    scroller?: Scroller;
    message?: string;
    isShowGoTopButton?: boolean;
}
import HomeTab from "@bundle:com.example.harmonyhelloworld/entry/ets/pages/home/<USER>/HomeTab";
import HomeHeader from "@bundle:com.example.harmonyhelloworld/entry/ets/pages/home/<USER>/HomeHeader";
class Home extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.scroller = new Scroller();
        this.__message = new ObservedPropertySimplePU('Hello World', this, "message");
        this.__isShowGoTopButton = new ObservedPropertySimplePU(false, this, "isShowGoTopButton");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: Home_Params) {
        if (params.scroller !== undefined) {
            this.scroller = params.scroller;
        }
        if (params.message !== undefined) {
            this.message = params.message;
        }
        if (params.isShowGoTopButton !== undefined) {
            this.isShowGoTopButton = params.isShowGoTopButton;
        }
    }
    updateStateVars(params: Home_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__message.purgeDependencyOnElmtId(rmElmtId);
        this.__isShowGoTopButton.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__message.aboutToBeDeleted();
        this.__isShowGoTopButton.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private scroller: Scroller;
    private __message: ObservedPropertySimplePU<string>;
    get message() {
        return this.__message.get();
    }
    set message(newValue: string) {
        this.__message.set(newValue);
    }
    // 是否展示回到顶部按钮
    private __isShowGoTopButton: ObservedPropertySimplePU<boolean>;
    get isShowGoTopButton() {
        return this.__isShowGoTopButton.get();
    }
    set isShowGoTopButton(newValue: boolean) {
        this.__isShowGoTopButton.set(newValue);
    }
    aboutToAppear(): void {
        console.log('home aboutToAppear');
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/home/<USER>", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.linearGradient({
                angle: 180,
                colors: [['#fefefe', 0], ['#f2f4f5', 1]]
            });
        }, Column);
        {
            this.observeComponentCreation2((elmtId, isInitialRender) => {
                if (isInitialRender) {
                    let componentCall = new 
                    // 头部
                    HomeHeader(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/home/<USER>", line: 20, col: 7 });
                    ViewPU.create(componentCall);
                    let paramsLambda = () => {
                        return {};
                    };
                    componentCall.paramsGenerator_ = paramsLambda;
                }
                else {
                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                }
            }, { name: "HomeHeader" });
        }
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 内容主题
            Scroll.create(this.scroller);
            Scroll.debugLine("entry/src/main/ets/pages/home/<USER>", "entry");
            // 内容主题
            Scroll.width('100%');
            // 内容主题
            Scroll.layoutWeight(1);
            // 内容主题
            Scroll.onWillScroll(() => {
                const scrollYOffset = this.scroller.currentOffset().yOffset;
                this.isShowGoTopButton = scrollYOffset > 500 ? true : false;
            });
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/home/<USER>", "entry");
            Column.width('100%');
            Column.margin({ bottom: 15 });
        }, Column);
        {
            this.observeComponentCreation2((elmtId, isInitialRender) => {
                if (isInitialRender) {
                    let componentCall = new HomeTab(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/home/<USER>", line: 25, col: 11 });
                    ViewPU.create(componentCall);
                    let paramsLambda = () => {
                        return {};
                    };
                    componentCall.paramsGenerator_ = paramsLambda;
                }
                else {
                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                }
            }, { name: "HomeTab" });
        }
        Column.pop();
        // 内容主题
        Scroll.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isShowGoTopButton) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithChild({ type: ButtonType.Circle, stateEffect: true });
                        Button.debugLine("entry/src/main/ets/pages/home/<USER>", "entry");
                        Button.width(35);
                        Button.height(35);
                        Button.backgroundColor('#fff');
                        Button.shadow({
                            radius: 3,
                            color: '#666',
                            offsetX: 2,
                            offsetY: 2
                        });
                        Button.position({
                            bottom: 10,
                            right: 10
                        });
                        Button.onClick(() => {
                            this.scroller.scrollEdge(Edge.Top);
                        });
                    }, Button);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Image.create({ "id": 16777285, "type": 20000, params: [], "bundleName": "com.example.harmonyhelloworld", "moduleName": "entry" });
                        Image.debugLine("entry/src/main/ets/pages/home/<USER>", "entry");
                        Image.width(20);
                        Image.fillColor('#666');
                    }, Image);
                    Button.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
export default Home;
