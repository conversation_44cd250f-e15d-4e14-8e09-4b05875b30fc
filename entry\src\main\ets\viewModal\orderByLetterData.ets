
export interface CountryData {
  label: string;
  value: string;
}

export interface OrderByLetterType {
  a: CountryData[]
  b: CountryData[]
  c: CountryData[]
  d: CountryData[]
  e: CountryData[]
  f: CountryData[]
  g: CountryData[]
  h: CountryData[]
  j: CountryData[]
  k: CountryData[]
  l: CountryData[]
  m: CountryData[]
  n: CountryData[]
  p: CountryData[]
  r: CountryData[]
  s: CountryData[]
  t: CountryData[]
  w: CountryData[]
  x: CountryData[]
  y: CountryData[]
  z: CountryData[]
}

export const orderByLetter: OrderByLetterType = {
  "a": [
    {
      "label": "阿尔巴尼亚  +355",
      "value": "al,+355,7"
    },
    {
      "label": "阿尔及利亚  +213",
      "value": "dz,+213,5"
    },
    {
      "label": "阿富汗  +93",
      "value": "af,+93,5"
    },
    {
      "label": "阿根廷  +54",
      "value": "ar,+54,5"
    },
    {
      "label": "爱尔兰  +353",
      "value": "ie,+353,7"
    },
    {
      "label": "埃及  +20",
      "value": "eg,+20,5"
    },
    {
      "label": "埃塞俄比亚  +251",
      "value": "et,+251,5"
    },
    {
      "label": "爱沙尼亚  +372",
      "value": "ee,+372,7"
    },
    {
      "label": "阿拉伯联合酋长国  +971",
      "value": "ae,+971,5"
    },
    {
      "label": "阿曼  +968",
      "value": "om,+968,5"
    },
    {
      "label": "安道尔  +376",
      "value": "ad,+376,7"
    },
    {
      "label": "安哥拉  +244",
      "value": "ao,+244,5"
    },
    {
      "label": "澳大利亚  +61",
      "value": "au,+61,7"
    },
    {
      "label": "奥地利  +43",
      "value": "at,+43,7"
    },
    {
      "label": "阿塞拜疆  +994",
      "value": "az,+994,5"
    }
  ],
  "b": [
    {
      "label": "巴布亚新几内亚  +675",
      "value": "pg,+675,5"
    },
    {
      "label": "巴哈马  +1242",
      "value": "bs,+1242,5"
    },
    {
      "label": "白俄罗斯  +375",
      "value": "by,+375,5"
    },
    {
      "label": "巴基斯坦  +92",
      "value": "pk,+92,5"
    },
    {
      "label": "巴拉圭  +595",
      "value": "py,+595,5"
    },
    {
      "label": "巴勒斯坦  +970",
      "value": "ps,+970,5"
    },
    {
      "label": "巴林  +973",
      "value": "bh,+973,5"
    },
    {
      "label": "巴拿马  +507",
      "value": "pa,+507,5"
    },
    {
      "label": "保加利亚  +359",
      "value": "bg,+359,7"
    },
    {
      "label": "巴西  +55",
      "value": "br,+55,5"
    },
    {
      "label": "北马其顿  +389",
      "value": "mk,+389,7"
    },
    {
      "label": "贝宁  +229",
      "value": "bj,+229,5"
    },
    {
      "label": "比利时  +32",
      "value": "be,+32,7"
    },
    {
      "label": "冰岛  +354",
      "value": "is,+354,7"
    },
    {
      "label": "博茨瓦纳  +267",
      "value": "bw,+267,5"
    },
    {
      "label": "波兰  +48",
      "value": "pl,+48,7"
    },
    {
      "label": "玻利维亚  +591",
      "value": "bo,+591,5"
    },
    {
      "label": "波斯尼亚和黑塞哥维那  +387",
      "value": "ba,+387,7"
    },
    {
      "label": "布基纳法索  +226",
      "value": "bf,+226,5"
    },
    {
      "label": "布隆迪  +257",
      "value": "bi,+257,5"
    }
  ],
  "c": [
    {
      "label": "赤道几内亚  +240",
      "value": "gq,+240,5"
    }
  ],
  "d": [
    {
      "label": "丹麦  +45",
      "value": "dk,+45,7"
    },
    {
      "label": "德国  +49",
      "value": "de,+49,7"
    },
    {
      "label": "多哥  +228",
      "value": "tg,+228,5"
    },
    {
      "label": "多米尼加共和国  +1849",
      "value": "do,+1849,5"
    },
    {
      "label": "多米尼加共和国  +1809",
      "value": "do,+1809,5"
    },
    {
      "label": "多米尼加共和国  +1829",
      "value": "do,+1829,5"
    }
  ],
  "e": [
    {
      "label": "厄瓜多尔  +593",
      "value": "ec,+593,5"
    },
    {
      "label": "俄罗斯  +7",
      "value": "ru,+7,8"
    }
  ],
  "f": [
    {
      "label": "法国  +33",
      "value": "fr,+33,7"
    },
    {
      "label": "法罗群岛  +298",
      "value": "fo,+298,7"
    },
    {
      "label": "梵蒂冈  +379",
      "value": "va,+379,7"
    },
    {
      "label": "法属波利尼西亚  +689",
      "value": "pf,+689,5"
    },
    {
      "label": "法属圭亚那  +594",
      "value": "gf,+594,5"
    },
    {
      "label": "斐济  +679",
      "value": "fj,+679,5"
    },
    {
      "label": "菲律宾  +63",
      "value": "ph,+63,5"
    },
    {
      "label": "芬兰  +358",
      "value": "fi,+358,7"
    },
    {
      "label": "佛得角  +238",
      "value": "cv,+238,5"
    }
  ],
  "g": [
    {
      "label": "冈比亚  +220",
      "value": "gm,+220,5"
    },
    {
      "label": "刚果（布）  +242",
      "value": "cg,+242,5"
    },
    {
      "label": "刚果（金）  +243",
      "value": "cd,+243,5"
    },
    {
      "label": "格陵兰岛  +299",
      "value": "gl,+299,7"
    },
    {
      "label": "格鲁吉亚  +995",
      "value": "ge,+995,5"
    },
    {
      "label": "哥伦比亚  +57",
      "value": "co,+57,5"
    },
    {
      "label": "哥斯达黎加  +506",
      "value": "cr,+506,5"
    },
    {
      "label": "瓜德罗普  +590",
      "value": "gp,+590,5"
    }
  ],
  "h": [
    {
      "label": "海地  +509",
      "value": "ht,+509,5"
    },
    {
      "label": "韩国  +82",
      "value": "kr,+82,5"
    },
    {
      "label": "哈萨克斯坦  +7",
      "value": "kz,+7,5"
    },
    {
      "label": "黑山  +382",
      "value": "me,+382,7"
    },
    {
      "label": "荷兰  +31",
      "value": "nl,+31,7"
    },
    {
      "label": "洪都拉斯  +504",
      "value": "hn,+504,5"
    }
  ],
  "j": [
    {
      "label": "加纳  +233",
      "value": "gh,+233,5"
    },
    {
      "label": "加拿大  +1",
      "value": "ca,+1,7"
    },
    {
      "label": "柬埔寨  +855",
      "value": "kh,+855,5"
    },
    {
      "label": "加蓬  +241",
      "value": "ga,+241,5"
    },
    {
      "label": "吉布提  +253",
      "value": "dj,+253,5"
    },
    {
      "label": "捷克共和国  +420",
      "value": "cz,+420,7"
    },
    {
      "label": "吉尔吉斯斯坦  +996",
      "value": "kg,+996,5"
    },
    {
      "label": "津巴布韦  +263",
      "value": "zw,+263,5"
    },
    {
      "label": "几内亚  +224",
      "value": "gn,+224,5"
    },
    {
      "label": "几内亚比绍  +245",
      "value": "gw,+245,5"
    }
  ],
  "k": [
    {
      "label": "喀麦隆  +237",
      "value": "cm,+237,5"
    },
    {
      "label": "卡塔尔  +974",
      "value": "qa,+974,5"
    },
    {
      "label": "克罗地亚  +385",
      "value": "hr,+385,7"
    },
    {
      "label": "科摩罗  +269",
      "value": "km,+269,5"
    },
    {
      "label": "肯尼亚  +254",
      "value": "ke,+254,5"
    },
    {
      "label": "科特迪瓦  +225",
      "value": "ci,+225,5"
    },
    {
      "label": "科威特  +965",
      "value": "kw,+965,5"
    },
    {
      "label": "库克群岛  +682",
      "value": "ck,+682,5"
    }
  ],
  "l": [
    {
      "label": "莱索托  +266",
      "value": "ls,+266,5"
    },
    {
      "label": "老挝  +856",
      "value": "la,+856,5"
    },
    {
      "label": "拉脱维亚  +371",
      "value": "lv,+371,7"
    },
    {
      "label": "黎巴嫩  +961",
      "value": "lb,+961,5"
    },
    {
      "label": "利比里亚  +231",
      "value": "lr,+231,5"
    },
    {
      "label": "利比亚  +218",
      "value": "ly,+218,5"
    },
    {
      "label": "列支敦士登  +423",
      "value": "li,+423,7"
    },
    {
      "label": "立陶宛  +370",
      "value": "lt,+370,7"
    },
    {
      "label": "留尼汪  +262",
      "value": "re,+262,5"
    },
    {
      "label": "罗马尼亚  +40",
      "value": "ro,+40,7"
    },
    {
      "label": "卢森堡  +352",
      "value": "lu,+352,7"
    },
    {
      "label": "卢旺达  +250",
      "value": "rw,+250,5"
    }
  ],
  "m": [
    {
      "label": "马达加斯加  +261",
      "value": "mg,+261,5"
    },
    {
      "label": "马尔代夫  +960",
      "value": "mv,+960,5"
    },
    {
      "label": "马耳他  +356",
      "value": "mt,+356,7"
    },
    {
      "label": "马来西亚  +60",
      "value": "my,+60,5"
    },
    {
      "label": "马拉维  +265",
      "value": "mw,+265,5"
    },
    {
      "label": "马里  +223",
      "value": "ml,+223,5"
    },
    {
      "label": "毛里求斯  +230",
      "value": "mu,+230,5"
    },
    {
      "label": "毛里塔尼亚  +222",
      "value": "mr,+222,5"
    },
    {
      "label": "马提尼克岛  +596",
      "value": "mq,+596,5"
    },
    {
      "label": "马约特  +269",
      "value": "yt,+269,5"
    },
    {
      "label": "美国  +1",
      "value": "us,+1,7"
    },
    {
      "label": "蒙古  +976",
      "value": "mn,+976,5"
    },
    {
      "label": "孟加拉  +880",
      "value": "bd,+880,5"
    },
    {
      "label": "缅甸  +95",
      "value": "mm,+95,5"
    },
    {
      "label": "秘鲁  +51",
      "value": "pe,+51,5"
    },
    {
      "label": "摩尔多瓦  +373",
      "value": "md,+373,7"
    },
    {
      "label": "摩洛哥  +212",
      "value": "ma,+212,5"
    },
    {
      "label": "摩纳哥  +377",
      "value": "mc,+377,7"
    },
    {
      "label": "莫桑比克  +258",
      "value": "mz,+258,5"
    },
    {
      "label": "墨西哥  +52",
      "value": "mx,+52,5"
    }
  ],
  "n": [
    {
      "label": "纳米比亚  +264",
      "value": "na,+264,5"
    },
    {
      "label": "南非  +27",
      "value": "za,+27,5"
    },
    {
      "label": "瑙鲁  +674",
      "value": "nr,+674,5"
    },
    {
      "label": "尼泊尔  +977",
      "value": "np,+977,5"
    },
    {
      "label": "尼加拉瓜  +505",
      "value": "ni,+505,5"
    },
    {
      "label": "尼日尔  +227",
      "value": "ne,+227,5"
    },
    {
      "label": "尼日利亚  +234",
      "value": "ng,+234,5"
    },
    {
      "label": "挪威  +47",
      "value": "no,+47,7"
    }
  ],
  "p": [
    {
      "label": "葡萄牙  +351",
      "value": "pt,+351,7"
    }
  ],
  "r": [
    {
      "label": "日本  +81",
      "value": "jp,+81,5"
    },
    {
      "label": "瑞典  +46",
      "value": "se,+46,7"
    },
    {
      "label": "瑞士  +41",
      "value": "ch,+41,7"
    }
  ],
  "s": [
    {
      "label": "萨尔瓦多  +503",
      "value": "sv,+503,5"
    },
    {
      "label": "塞尔维亚  +381",
      "value": "rs,+381,7"
    },
    {
      "label": "塞拉利昂  +232",
      "value": "sl,+232,5"
    },
    {
      "label": "塞内加尔  +221",
      "value": "sn,+221,5"
    },
    {
      "label": "塞浦路斯  +357",
      "value": "cy,+357,7"
    },
    {
      "label": "塞舌尔  +248",
      "value": "sc,+248,5"
    },
    {
      "label": "沙特阿拉伯  +966",
      "value": "sa,+966,5"
    },
    {
      "label": "圣马力诺  +378",
      "value": "sm,+378,7"
    },
    {
      "label": "斯里兰卡  +94",
      "value": "lk,+94,5"
    },
    {
      "label": "斯洛伐克  +421",
      "value": "sk,+421,7"
    },
    {
      "label": "斯洛文尼亚  +386",
      "value": "si,+386,7"
    },
    {
      "label": "斯威士兰  +268",
      "value": "sz,+268,5"
    },
    {
      "label": "苏里南  +597",
      "value": "sr,+597,5"
    },
    {
      "label": "所罗门群岛  +677",
      "value": "sb,+677,5"
    },
    {
      "label": "索马里  +252",
      "value": "so,+252,5"
    }
  ],
  "t": [
    {
      "label": "泰国  +66",
      "value": "th,+66,5"
    },
    {
      "label": "塔吉克斯坦  +992",
      "value": "tj,+992,5"
    },
    {
      "label": "汤加  +676",
      "value": "to,+676,5"
    },
    {
      "label": "坦桑尼亚  +255",
      "value": "tz,+255,5"
    },
    {
      "label": "特立尼达和多巴哥  +1868",
      "value": "tt,+1868,5"
    },
    {
      "label": "土耳其  +90",
      "value": "tr,+90,7"
    },
    {
      "label": "土库曼斯坦  +993",
      "value": "tm,+993,5"
    },
    {
      "label": "突尼斯  +216",
      "value": "tn,+216,5"
    }
  ],
  "w": [
    {
      "label": "危地马拉  +502",
      "value": "gt,+502,5"
    },
    {
      "label": "委内瑞拉  +58",
      "value": "ve,+58,5"
    },
    {
      "label": "文莱  +673",
      "value": "bn,+673,5"
    },
    {
      "label": "乌干达  +256",
      "value": "ug,+256,5"
    },
    {
      "label": "乌克兰  +380",
      "value": "ua,+380,7"
    },
    {
      "label": "乌拉圭  +598",
      "value": "uy,+598,5"
    },
    {
      "label": "乌兹别克斯坦  +998",
      "value": "uz,+998,5"
    }
  ],
  "x": [
    {
      "label": "西班牙  +34",
      "value": "es,+34,7"
    },
    {
      "label": "希腊  +30",
      "value": "gr,+30,7"
    },
    {
      "label": "新加坡  +65",
      "value": "sg,+65,5"
    },
    {
      "label": "新西兰  +64",
      "value": "nz,+64,7"
    },
    {
      "label": "匈牙利  +36",
      "value": "hu,+36,7"
    }
  ],
  "y": [
    {
      "label": "牙买加  +1876",
      "value": "jm,+1876,5"
    },
    {
      "label": "亚美尼亚  +374",
      "value": "am,+374,5"
    },
    {
      "label": "也门  +967",
      "value": "ye,+967,5"
    },
    {
      "label": "意大利  +39",
      "value": "it,+39,7"
    },
    {
      "label": "伊拉克  +964",
      "value": "iq,+964,5"
    },
    {
      "label": "印度  +91",
      "value": "in,+91,5"
    },
    {
      "label": "印度尼西亚  +62",
      "value": "id,+62,5"
    },
    {
      "label": "英国  +44",
      "value": "gb,+44,7"
    },
    {
      "label": "以色列  +972",
      "value": "il,+972,7"
    },
    {
      "label": "约旦  +962",
      "value": "jo,+962,5"
    },
    {
      "label": "越南  +84",
      "value": "vn,+84,5"
    }
  ],
  "z": [
    {
      "label": "赞比亚  +260",
      "value": "zm,+260,5"
    },
    {
      "label": "乍得  +235",
      "value": "td,+235,5"
    },
    {
      "label": "直布罗陀  +350",
      "value": "gi,+350,7"
    },
    {
      "label": "智利  +56",
      "value": "cl,+56,5"
    },
    {
      "label": "中非共和国  +236",
      "value": "cf,+236,5"
    },
    {
      "label": "中国  +86",
      "value": "cn,+86,1"
    },
    {
      "label": "中国澳门  +853",
      "value": "mo,+853,5"
    },
    {
      "label": "中国台湾  +886",
      "value": "tw,+886,5"
    },
    {
      "label": "中国香港  +852",
      "value": "hk,+852,5"
    }
  ]
}