{"resolveConflictMode": true, "depName2RootPath": {"@ohos/axios": "D:\\DevEcoStudio\\DevEcoStudio\\harmonyProject-master\\oh_modules\\.ohpm\\@ohos+axios@2.2.2\\oh_modules\\@ohos\\axios", "@ohos/hypium": "D:\\DevEcoStudio\\DevEcoStudio\\harmonyProject-master\\oh_modules\\.ohpm\\@ohos+hypium@1.0.18\\oh_modules\\@ohos\\hypium", "@ohos/hamock": "D:\\DevEcoStudio\\DevEcoStudio\\harmonyProject-master\\oh_modules\\.ohpm\\@ohos+hamock@1.0.0\\oh_modules\\@ohos\\hamock"}, "depName2DepInfo": {"@ohos/axios": {"pkgRootPath": "D:\\DevEcoStudio\\DevEcoStudio\\harmonyProject-master\\oh_modules\\.ohpm\\@ohos+axios@2.2.2\\oh_modules\\@ohos\\axios", "pkgName": "@ohos/axios", "pkgVersion": "2.2.2"}, "@ohos/hypium": {"pkgRootPath": "D:\\DevEcoStudio\\DevEcoStudio\\harmonyProject-master\\oh_modules\\.ohpm\\@ohos+hypium@1.0.18\\oh_modules\\@ohos\\hypium", "pkgName": "@ohos/hypium", "pkgVersion": "1.0.18"}, "@ohos/hamock": {"pkgRootPath": "D:\\DevEcoStudio\\DevEcoStudio\\harmonyProject-master\\oh_modules\\.ohpm\\@ohos+hamock@1.0.0\\oh_modules\\@ohos\\hamock", "pkgName": "@ohos/hamock", "pkgVersion": "1.0.0"}}}