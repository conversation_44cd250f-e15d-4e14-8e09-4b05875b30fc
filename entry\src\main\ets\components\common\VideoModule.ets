import { deviceInfo } from '@kit.BasicServicesKit';

@Component
struct VideoModule {
  @State videoSrc: Resource = $rawfile('video.mp4')
  @State previewUri: Resource = $r('app.media.classify02')
  @State curRate: PlaybackSpeed = PlaybackSpeed.Speed_Forward_1_00_X
  @State isAutoPlay: boolean = false
  @State showControls: boolean = false
  @State isPlay: boolean = false
  @State deviceType: string = ''
  controller: VideoController = new VideoController()

  aboutToAppear() {
    this.deviceType = deviceInfo.deviceType
  }

  build() {
    if (this.deviceType) {
      Stack() {
        Video({
          src: this.videoSrc,
          previewUri: this.previewUri,
          currentProgressRate: this.curRate,
          controller: this.controller
        })
          .width('100%')
          .height('100%')
          .autoPlay(this.isAutoPlay)
          .controls(this.showControls)
          .onStart(() => {
            console.info('onStart')
          })
          .onPause(() => {
            console.info('onPause')
          })
          .onFinish(() => {
            console.info('onFinish')
          })
          .onError(() => {
            console.info('onError')
          })
          .onStop(() => {
            console.info('onStop')
          })
          .onPrepared((e?: DurationObject) => {
            if (e != undefined) {
              console.info('onPrepared is ' + e.duration)
            }
          })
          .onSeeking((e?: TimeObject) => {
            if (e != undefined) {
              console.info('onSeeking is ' + e.time)
            }
          })
          .onSeeked((e?: TimeObject) => {
            if (e != undefined) {
              console.info('onSeeked is ' + e.time)
            }
          })
          .onUpdate((e?: TimeObject) => {
            if (e != undefined) {
              console.info('onUpdate is ' + e.time)
            }
          })
          .onClick(() => {
            this.isPlay = false
            this.controller.pause() // 暂停播放
          })

        if (!this.isPlay) {
          Row() {
            Button() {
              Text('\ue717')
                .fontFamily('sysFont')
                .fontSize(26)
                .fontColor('#fff')
            }.onClick(() => {
              this.isPlay = true
              this.controller.start()
            }).type(ButtonType.Circle)
            .backgroundColor('#33484444')
            .padding(10)
          }
        }


      }
    } else {
      Text('预览器无法加载视频')
        .margin({ top: 50 })
    }
  }
}

interface DurationObject {
  duration: number;
}

interface TimeObject {
  time: number;
}

export default VideoModule