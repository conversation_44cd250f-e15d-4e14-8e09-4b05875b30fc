import httpRequest from './httpRequest';
import { User, LoginRequest, RegisterRequest, ApiResponse } from '../models/User';

// JSON Server 基础URL
const BASE_URL = 'http://localhost:3000';

class AuthApi {

  /**
   * 用户登录
   * @param loginData 登录数据
   * @returns Promise<ApiResponse<User>>
   */
  async login(loginData: LoginRequest): Promise<ApiResponse<User>> {
    try {
      // 获取所有用户进行验证
      const response = await httpRequest({
        url: `${BASE_URL}/users`,
        method: 'get'
      });

      const users: User[] = response as User[];

      // 查找匹配的用户
      const user = users.find(u => u.phone === loginData.phone);

      if (!user) {
        return {
          success: false,
          message: '用户不存在'
        };
      }

      // 验证密码（如果是密码登录）
      if (loginData.password && user.password !== loginData.password) {
        return {
          success: false,
          message: '密码错误'
        };
      }

      // 验证码登录逻辑（这里简化处理，实际应该验证验证码）
      if (loginData.code && loginData.code !== '1234') {
        return {
          success: false,
          message: '验证码错误'
        };
      }

      // 登录成功，返回用户信息（不包含密码）
      const { password, ...userInfo } = user;
      return {
        success: true,
        data: userInfo,
        message: '登录成功'
      };

    } catch (error) {
      console.error('登录失败:', error);
      return {
        success: false,
        message: '登录失败，请稍后重试'
      };
    }
  }

  /**
   * 用户注册
   * @param registerData 注册数据
   * @returns Promise<ApiResponse<User>>
   */
  async register(registerData: RegisterRequest): Promise<ApiResponse<User>> {
    try {
      // 先检查用户是否已存在
      const existingUsers = await httpRequest({
        url: `${BASE_URL}/users`,
        method: 'get'
      });

      const users: User[] = existingUsers as User[];
      const existingUser = users.find(u => u.phone === registerData.phone);

      if (existingUser) {
        return {
          success: false,
          message: '该手机号已注册'
        };
      }

      // 创建新用户
      const newUser: User = {
        name: registerData.name,
        phone: registerData.phone,
        password: registerData.password,
        email: registerData.email || ''
      };

      const response = await httpRequest({
        url: `${BASE_URL}/users`,
        method: 'post',
        data: newUser
      });

      const createdUser = response as User;

      // 返回用户信息（不包含密码）
      const { password, ...userInfo } = createdUser;
      return {
        success: true,
        data: userInfo,
        message: '注册成功'
      };

    } catch (error) {
      console.error('注册失败:', error);
      return {
        success: false,
        message: '注册失败，请稍后重试'
      };
    }
  }

  /**
   * 发送验证码（模拟）
   * @param phone 手机号
   * @returns Promise<ApiResponse>
   */
  async sendVerificationCode(phone: string): Promise<ApiResponse> {
    try {
      // 这里模拟发送验证码的过程
      // 实际项目中应该调用短信服务API
      console.log(`向 ${phone} 发送验证码: 1234`);

      return {
        success: true,
        message: '验证码已发送'
      };
    } catch (error) {
      console.error('发送验证码失败:', error);
      return {
        success: false,
        message: '发送验证码失败'
      };
    }
  }
}

export default new AuthApi();