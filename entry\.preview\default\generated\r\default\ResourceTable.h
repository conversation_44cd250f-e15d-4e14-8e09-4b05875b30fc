/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef RESOURCE_TABLE_H
#define RESOURCE_TABLE_H

#include<stdint.h>

namespace OHOS {
const int32_t STRING_ENTRYABILITY_DESC = 0x0100000c;
const int32_t STRING_ENTRYABILITY_LABEL = 0x0100000d;
const int32_t STRING_APP_NAME = 0x01000000;
const int32_t STRING_MODULE_DESC = 0x0100000e;
const int32_t STRING_PAGE_SHOW = 0x01000040;
const int32_t COLOR_GRAY_FONT = 0x01000035;
const int32_t COLOR_MAIN_BG = 0x01000036;
const int32_t COLOR_MAIN_COLOR = 0x01000037;
const int32_t COLOR_START_WINDOW_BACKGROUND = 0x01000038;
const int32_t MEDIA_APP_ICON = 0x01000001;
const int32_t MEDIA_ARROW_DOWN_FILLING = 0x0100003f;
const int32_t MEDIA_ARROW_UP_FILLING = 0x01000032;
const int32_t MEDIA_BACKGROUND = 0x01000023;
const int32_t MEDIA_BAG = 0x01000051;
const int32_t MEDIA_BANNER1 = 0x01000046;
const int32_t MEDIA_BANNER2 = 0x01000047;
const int32_t MEDIA_BANNER3 = 0x01000027;
const int32_t MEDIA_BANNER4 = 0x01000020;
const int32_t MEDIA_BANNER5 = 0x0100003c;
const int32_t MEDIA_CAR = 0x01000013;
const int32_t MEDIA_CLASSIFY01 = 0x01000021;
const int32_t MEDIA_CLASSIFY010 = 0x01000009;
const int32_t MEDIA_CLASSIFY011 = 0x01000008;
const int32_t MEDIA_CLASSIFY012 = 0x01000026;
const int32_t MEDIA_CLASSIFY013 = 0x01000007;
const int32_t MEDIA_CLASSIFY014 = 0x0100001a;
const int32_t MEDIA_CLASSIFY015 = 0x01000003;
const int32_t MEDIA_CLASSIFY016 = 0x0100001d;
const int32_t MEDIA_CLASSIFY017 = 0x01000017;
const int32_t MEDIA_CLASSIFY018 = 0x01000015;
const int32_t MEDIA_CLASSIFY019 = 0x0100001c;
const int32_t MEDIA_CLASSIFY02 = 0x0100003e;
const int32_t MEDIA_CLASSIFY020 = 0x0100001f;
const int32_t MEDIA_CLASSIFY03 = 0x0100003d;
const int32_t MEDIA_CLASSIFY04 = 0x0100002b;
const int32_t MEDIA_CLASSIFY05 = 0x01000034;
const int32_t MEDIA_CLASSIFY06 = 0x01000022;
const int32_t MEDIA_CLASSIFY07 = 0x0100000b;
const int32_t MEDIA_CLASSIFY08 = 0x0100002d;
const int32_t MEDIA_CLASSIFY09 = 0x01000016;
const int32_t MEDIA_COMMENTS = 0x01000044;
const int32_t MEDIA_CONVEMPTYCART = 0x0100002e;
const int32_t MEDIA_DELETE = 0x0100002c;
const int32_t MEDIA_EAR = 0x0100004b;
const int32_t MEDIA_FOREGROUND = 0x01000039;
const int32_t MEDIA_HOT_RECOMMEND_WAP = 0x01000031;
const int32_t MEDIA_HUODONG01 = 0x01000018;
const int32_t MEDIA_IC_CONTACTS_BUSINESS_CARDS_FILLED = 0x01000002;
const int32_t MEDIA_IC_HEADER_LOGO = 0x01000028;
const int32_t MEDIA_IC_PUBLIC_ADD_NORM = 0x01000025;
const int32_t MEDIA_IC_PUBLIC_APPSTORE = 0x01000024;
const int32_t MEDIA_IC_PUBLIC_ARROW_DOWN_0 = 0x0100002a;
const int32_t MEDIA_IC_PUBLIC_ARROW_RIGHT = 0x01000011;
const int32_t MEDIA_IC_PUBLIC_BACK = 0x0100003a;
const int32_t MEDIA_IC_PUBLIC_COMMENTS = 0x01000010;
const int32_t MEDIA_IC_PUBLIC_CONTACTS = 0x0100000a;
const int32_t MEDIA_IC_PUBLIC_HOME_FILLED = 0x01000019;
const int32_t MEDIA_IC_PUBLIC_SEARCH = 0x0100002f;
const int32_t MEDIA_IC_PUBLIC_SETTINGS = 0x0100003b;
const int32_t MEDIA_IC_PUBLIC_TIMER = 0x0100001e;
const int32_t MEDIA_IC_PUBLIC_VIEW_GRID = 0x01000033;
const int32_t MEDIA_LAYERED_IMAGE = 0x0100001b;
const int32_t MEDIA_LOGO = 0x01000041;
const int32_t MEDIA_LOGO_RED = 0x0100000f;
const int32_t MEDIA_PHONE = 0x01000048;
const int32_t MEDIA_PHONE2 = 0x01000005;
const int32_t MEDIA_PIC1 = 0x0100004f;
const int32_t MEDIA_PIC2 = 0x0100004c;
const int32_t MEDIA_PIC3 = 0x0100004e;
const int32_t MEDIA_PIC4 = 0x0100004d;
const int32_t MEDIA_RECOMMEND01 = 0x01000004;
const int32_t MEDIA_RECOMMEND02 = 0x01000029;
const int32_t MEDIA_RECOMMEND03 = 0x01000006;
const int32_t MEDIA_SEARCH = 0x01000043;
const int32_t MEDIA_SETTING = 0x01000050;
const int32_t MEDIA_SHOPCART = 0x01000042;
const int32_t MEDIA_STAR = 0x0100004a;
const int32_t MEDIA_STARTICON = 0x01000030;
const int32_t MEDIA_TINYCART = 0x01000049;
const int32_t MEDIA_TOTOP = 0x01000045;
const int32_t PROFILE_BACKUP_CONFIG = 0x01000012;
const int32_t PROFILE_MAIN_PAGES = 0x01000014;
}
#endif