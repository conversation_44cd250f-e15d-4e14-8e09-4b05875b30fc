if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface NewProduct_Params {
}
import VideoModule from "@bundle:com.example.harmonyhelloworld/entry/ets/components/common/VideoModule";
import FollowButton from "@bundle:com.example.harmonyhelloworld/entry/ets/components/common/FollowButton";
class NewProduct extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: NewProduct_Params) {
    }
    updateStateVars(params: NewProduct_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
    }
    aboutToBeDeleted() {
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(6:5)", "entry");
            Scroll.width('100%');
            Scroll.height('100%');
            Scroll.scrollBar(BarState.Off);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(7:7)", "entry");
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('优质作者推荐');
            Text.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(8:9)", "entry");
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(9:9)", "entry");
            Column.backgroundColor('#fff');
            Column.width('100%');
            Column.borderRadius(15);
            Column.margin({ top: 6, bottom: 6 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(10:11)", "entry");
            Row.width('100%');
            Row.justifyContent(FlexAlign.SpaceAround);
            Row.padding({
                top: 15,
                bottom: 15
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777295, "type": 20000, params: [], "bundleName": "com.example.harmonyhelloworld", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(11:13)", "entry");
            Image.width(50);
            Image.borderRadius(500);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('快乐梦想家小王');
            Text.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(14:13)", "entry");
            Text.fontWeight(600);
        }, Text);
        Text.pop();
        {
            this.observeComponentCreation2((elmtId, isInitialRender) => {
                if (isInitialRender) {
                    let componentCall = new FollowButton(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/activity/product/NewProduct.ets", line: 16, col: 13 });
                    ViewPU.create(componentCall);
                    let paramsLambda = () => {
                        return {};
                    };
                    componentCall.paramsGenerator_ = paramsLambda;
                }
                else {
                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                }
            }, { name: "FollowButton" });
        }
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777292, "type": 20000, params: [], "bundleName": "com.example.harmonyhelloworld", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(25:11)", "entry");
            Image.width('100%');
            Image.borderRadius({
                topLeft: 15,
                topRight: 15
            });
        }, Image);
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 视频
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(39:9)", "entry");
            // 视频
            Column.backgroundColor('#fff');
            // 视频
            Column.width('100%');
            // 视频
            Column.height(230);
            // 视频
            Column.borderRadius(15);
            // 视频
            Column.clip(true);
            // 视频
            Column.margin({ top: 6, bottom: 6 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(40:11)", "entry");
            Row.width('100%');
            Row.justifyContent(FlexAlign.SpaceAround);
            Row.padding({
                top: 15,
                bottom: 15
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('新起点');
            Text.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(41:13)", "entry");
            Text.fontWeight(600);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            __Common__.create();
            __Common__.height(180);
        }, __Common__);
        {
            this.observeComponentCreation2((elmtId, isInitialRender) => {
                if (isInitialRender) {
                    let componentCall = new VideoModule(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/activity/product/NewProduct.ets", line: 50, col: 11 });
                    ViewPU.create(componentCall);
                    let paramsLambda = () => {
                        return {};
                    };
                    componentCall.paramsGenerator_ = paramsLambda;
                }
                else {
                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                }
            }, { name: "VideoModule" });
        }
        __Common__.pop();
        // 视频
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(60:9)", "entry");
            Column.backgroundColor('#fff');
            Column.width('100%');
            Column.borderRadius(15);
            Column.margin({ top: 6, bottom: 6 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(61:11)", "entry");
            Row.width('100%');
            Row.justifyContent(FlexAlign.SpaceAround);
            Row.padding({
                top: 15,
                bottom: 15
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777294, "type": 20000, params: [], "bundleName": "com.example.harmonyhelloworld", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(62:13)", "entry");
            Image.width(50);
            Image.borderRadius(500);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('快乐梦想家小王');
            Text.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(65:13)", "entry");
            Text.fontWeight(600);
        }, Text);
        Text.pop();
        {
            this.observeComponentCreation2((elmtId, isInitialRender) => {
                if (isInitialRender) {
                    let componentCall = new FollowButton(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/activity/product/NewProduct.ets", line: 67, col: 13 });
                    ViewPU.create(componentCall);
                    let paramsLambda = () => {
                        return {};
                    };
                    componentCall.paramsGenerator_ = paramsLambda;
                }
                else {
                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                }
            }, { name: "FollowButton" });
        }
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777293, "type": 20000, params: [], "bundleName": "com.example.harmonyhelloworld", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(75:11)", "entry");
            Image.width('100%');
            Image.borderRadius({
                topLeft: 15,
                topRight: 15
            });
        }, Image);
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(88:9)", "entry");
            Column.backgroundColor('#fff');
            Column.width('100%');
            Column.borderRadius(15);
            Column.margin({ top: 6, bottom: 6 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(89:11)", "entry");
            Row.width('100%');
            Row.justifyContent(FlexAlign.SpaceAround);
            Row.padding({
                top: 15,
                bottom: 15
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777295, "type": 20000, params: [], "bundleName": "com.example.harmonyhelloworld", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(90:13)", "entry");
            Image.width(50);
            Image.borderRadius(500);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('快乐梦想家小王');
            Text.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(93:13)", "entry");
            Text.fontWeight(600);
        }, Text);
        Text.pop();
        {
            this.observeComponentCreation2((elmtId, isInitialRender) => {
                if (isInitialRender) {
                    let componentCall = new FollowButton(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/activity/product/NewProduct.ets", line: 95, col: 13 });
                    ViewPU.create(componentCall);
                    let paramsLambda = () => {
                        return {};
                    };
                    componentCall.paramsGenerator_ = paramsLambda;
                }
                else {
                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                }
            }, { name: "FollowButton" });
        }
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777292, "type": 20000, params: [], "bundleName": "com.example.harmonyhelloworld", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/activity/product/NewProduct.ets(103:11)", "entry");
            Image.width('100%');
            Image.borderRadius({
                topLeft: 15,
                topRight: 15
            });
        }, Image);
        Column.pop();
        Column.pop();
        Scroll.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
export default NewProduct;
