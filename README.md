# <center> harmonyOs Project ArkTs 仿华为商城

选华为商城作为模仿对象，页面包含了各种元素、方法使用，便于全面学习理解

**主要包括以下功能模块：**

1. 首页（轮播图、横向滚动菜单等）
2. 分类（列表）
3. 发现
4. 购物车
5. 我的
6. 登录/注册
7. 国家地区选择菜单（带导航）
8. 接口请求
9. 页面跳转
10. 搜索模块（数据持久化）
11. 视频模块
12. 商品详情

**效果图**

![image](https://github.com/youfrweb/harmonyProject/blob/master/projectImages/projectImage01.png)
![image](https://github.com/youfrweb/harmonyProject/blob/master/projectImages/projectImage02.png)
![image](https://github.com/youfrweb/harmonyProject/blob/master/projectImages/projectImage03.png)
![image](https://github.com/youfrweb/harmonyProject/blob/master/projectImages/projectImage04.png)
![image](https://github.com/youfrweb/harmonyProject/blob/master/projectImages/projectImage05.png)
![image](https://github.com/youfrweb/harmonyProject/blob/master/projectImages/projectImage06.png)
![image](https://github.com/youfrweb/harmonyProject/blob/master/projectImages/projectImage07.png)
![image](https://github.com/youfrweb/harmonyProject/blob/master/projectImages/projectImage08.png)
![image](https://github.com/youfrweb/harmonyProject/blob/master/projectImages/projectImage09.png)

详情

![image](https://github.com/youfrweb/harmonyProject/blob/master/projectImages/projectImage10.png)
![image](https://github.com/youfrweb/harmonyProject/blob/master/projectImages/projectImage101.png)
![image](https://github.com/youfrweb/harmonyProject/blob/master/projectImages/projectImage102.png)

