interface ClassifyListType {
  text: string
  handle: () => void
}

@Component
struct HomeClassify {
  private scroller: <PERSON><PERSON><PERSON> = new <PERSON><PERSON>er()
  @State classifyList: ClassifyListType[] = [
      {text:'限时秒杀', handle: () => {}},
      {text:'手机优惠', handle: () => {}},
      {text:'签到领券', handle: () => {}},
      {text:'福利活动', handle: () => {}},
      {text:'国家补贴', handle: () => {}},
      {text:'以旧换新', handle: () => {}},
      {text:'免费定制', handle: () => {}},
      {text:'配件', handle: () => {}},
      {text:'体验店', handle: () => {}},
      {text:'售后服务', handle: () => {}},
      {text:'以旧换新', handle: () => {}},
      {text:'免费定制', handle: () => {}},
      {text:'配件', handle: () => {}},
      {text:'体验店', handle: () => {}},
      {text:'售后服务', handle: () => {}},
      {text:'以旧换新', handle: () => {}},
      {text:'限时秒杀', handle: () => {}},
      {text:'手机优惠', handle: () => {}},
      {text:'签到领券', handle: () => {}},
      {text:'福利活动', handle: () => {}},
      {text:'国家补贴', handle: () => {}},
  ]

  build() {
    Column() {
      Scroll(this.scroller) {
        Grid() {
          ForEach(this.classifyList, (item: ClassifyListType, index:number) => {
            GridItem() {
              Column() {
                Image($r(`app.media.classify0${index+1}`))
                  .width(50)
                Text(item.text)
                  .fontSize(12)
                  .fontColor('#d1c1c')
              }
              .width('100%')
              .height('100%')
            }
          })
        }
        .columnsTemplate('1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr')
        .rowsTemplate('1fr 1fr')
        .height(150)
        .width('200%')
      }
      .scrollable(ScrollDirection.Horizontal)
      .scrollBar(BarState.Off)

      ScrollBar({ scroller: this.scroller, direction: ScrollBarDirection.Horizontal, state: BarState.On }) {
        Text()
          .width(15)
          .height(5)
          .borderRadius(10)
          .backgroundColor('#000')
      }
      .width(30)
      .borderRadius(5)
      .backgroundColor('#ededed')
    }
    .margin({
      left: 12,
      right: 12,
      top: 10
    })
    .padding({
      bottom: 5
    })
  }
}

export default HomeClassify