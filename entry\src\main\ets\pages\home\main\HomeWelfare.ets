@Component
export struct HomeWelfare {

  // 顶部主会场构建器
  @Builder
  TopBanner() {
    Stack({ alignContent: Alignment.TopStart }) {
      // 主体内容区域
      Row() {
        // 左侧文字和按钮区域
        Column({ space: 8 }) {
          Stack({ alignContent: Alignment.TopStart }) {
            Text('618手机特惠')
              .fontSize(22)
              .fontWeight(FontWeight.Bold)
              .fontColor(Color.White)
              .padding({ left: 4 }) // 给左边留出一点空间

            // “国补”标签
            Text('国补')
              .fontSize(10)
              .fontColor('#228b22') // 暗绿色
              .padding({ left: 5, right: 5, top: 2, bottom: 2 })
              .backgroundColor('#d4edda') // 淡绿色背景
              .borderRadius(4)
              .border({
                width: 1,
                color: '#90ee90' // 亮绿色边框
              })
              .offset({ x: 125, y: -2 })
          }

          Text('S30 新品享 85 折')
            .fontSize(16)
            .fontWeight(FontWeight.Medium)
            .fontColor(Color.White)
            .padding({ left: 4 })

          // “立即前往”按钮
          Button({ type: ButtonType.Normal}) {
            Text('立即前往')
              .fontSize(16)
              .fontWeight(FontWeight.Bold)
              .fontColor('#333333')
          }
          .backgroundColor('#ffebc1')
          .borderRadius(16)
          .height(38)
          .margin({ top: 10 })
        }
        .alignItems(HorizontalAlign.Start)
        .padding(15)
        .flexGrow(1) // 占据左侧所有剩余空间

        // 右侧手机图片区域
        Stack() {
          // 后面的手机
          Image($r('app.media.phone2'))
            .width(120)
            .objectFit(ImageFit.Contain)
            .offset({ x: 20, y: -10 })
            .rotate({ angle: 10 })

          // 前面的手机
          Image($r('app.media.phone'))
            .width(110)
            .objectFit(ImageFit.Contain)
            .offset({ x: -20, y: 10 })
            .rotate({ angle: 10 })
        }
        .width('45%') // 分配给图片区域的宽度
        .align(Alignment.Center)
      }
      .width('100%')
      .height(160)
      .backgroundColor('#fa2c50') // 主红色背景
      .borderRadius(15)
      .clip(true) // 裁剪超出圆角的部分

      // 购物车和星星等装饰元素，使用position定位
      Image($r('app.media.star'))
        .width(30)
        .position({ x: 290, y: 100 })

      Image($r('app.media.tinyCart'))
        .width(80)
        .position({ x: 160, y: 80 })
    }

  }

  // 底部左侧卡片构建器
  @Builder
  BottomLeftCard() {
    Row() {
      Column({ space: 5 }) {
        Text('爆款配件')
          .fontSize(16)
          .fontWeight(FontWeight.Bold)
        Text('低至 9.9 元')
          .fontSize(12)
          .fontColor(Color.Gray)
        Button({ type: ButtonType.Normal }) {
          Text('前往')
            .fontSize(12)
            .fontColor(Color.White)
        }
        .width(60)
        .height(28)
        .borderRadius(14)
        .backgroundColor('#fa2c50')
        .margin({ top: 8 })
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)

      // 配件图片
      Stack({ alignContent: Alignment.BottomEnd }) {
        Image($r('app.media.recommend01'))
          .width(70)
          .height(70)
          .objectFit(ImageFit.Contain)
        Image($r('app.media.recommend02'))
          .width(40)
          .height(40)
          .objectFit(ImageFit.Contain)
          .offset({ x: -5, y: -5 })
      }
    }
    .width('100%')
    .padding(12)
    .backgroundColor(Color.White)
    .borderRadius(12)
  }

  // 底部右侧卡片构建器
  @Builder
  BottomRightCard() {
    Row() {
      Column({ space: 5 }) {
        Text('会员会场')
          .fontSize(16)
          .fontWeight(FontWeight.Bold)
        Text('瓜分1亿积分')
          .fontSize(12)
          .fontColor(Color.Gray)
        Button({ type: ButtonType.Normal }) {
          Text('前往')
            .fontSize(12)
            .fontColor(Color.White)
        }
        .width(60)
        .height(28)
        .borderRadius(14)
        .backgroundColor('#fa2c50')
        .margin({ top: 8 })
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)

      // 会员V标
      // TODO: 替换为您的会员V标图片
      Image($r('app.media.phone2'))
        .width(70)
        .height(70)
        .objectFit(ImageFit.Contain)
    }
    .width('100%')
    .padding(12)
    .backgroundColor(Color.White)
    .borderRadius(12)
  }

  build() {
    Column({ space: 10 }) {
      // 顶部Banner
      this.TopBanner()

      // 底部两个卡片
      Row({ space: 10 }) {
        Column() {
          this.BottomLeftCard()
        }.layoutWeight(1)

        Column() {
          this.BottomRightCard()
        }.layoutWeight(1)
      }
    }
    .width('100%')
    .padding(12)
    .backgroundColor('#f5f5f5')
  }
}

export default HomeWelfare

