import { router } from '@kit.ArkUI';
import AuthApi from '../../api/auth';
import { authStore } from '../../store/AuthStore';
import { LoginRequest, RegisterRequest } from '../../models/User';

interface ParamsType {
  label: string
}

@Entry
@Component
struct Login {
  @State message: string = '登录帐号以使用更多服务';
  @State isCodeLoginShow: boolean = true
  @State addressLabel: string = '中国 +86'
  @State isRegisterMode: boolean = false
  title: string = 'vivo'

  // 表单数据
  @State phoneNumber: string = ''
  @State password: string = ''
  @State verificationCode: string = ''
  @State userName: string = ''
  @State email: string = ''

  // UI状态
  @State isLoading: boolean = false
  @State errorMessage: string = ''
  @State successMessage: string = ''
  @State isCodeSent: boolean = false
  @State countdown: number = 0

  onPageShow(): void {
    if (router.getParams() !== undefined) {
      this.addressLabel = (router.getParams() as ParamsType).label
    }
  }

  // 清除消息
  clearMessages() {
    this.errorMessage = ''
    this.successMessage = ''
  }

  // 验证手机号
  validatePhone(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  }

  // 发送验证码
  async sendCode() {
    this.clearMessages()

    if (!this.validatePhone(this.phoneNumber)) {
      this.errorMessage = '请输入正确的手机号'
      return
    }

    this.isLoading = true
    try {
      const result = await AuthApi.sendVerificationCode(this.phoneNumber)
      if (result.success) {
        this.successMessage = result.message || '验证码已发送'
        this.isCodeSent = true
        this.startCountdown()
      } else {
        this.errorMessage = result.message || '发送验证码失败'
      }
    } catch (error) {
      this.errorMessage = '网络错误，请稍后重试'
    } finally {
      this.isLoading = false
    }
  }

  // 倒计时
  startCountdown() {
    this.countdown = 60
    const timer = setInterval(() => {
      this.countdown--
      if (this.countdown <= 0) {
        clearInterval(timer)
        this.isCodeSent = false
      }
    }, 1000)
  }

  // 验证码登录
  async loginWithCode() {
    this.clearMessages()

    if (!this.validatePhone(this.phoneNumber)) {
      this.errorMessage = '请输入正确的手机号'
      return
    }

    if (!this.verificationCode) {
      this.errorMessage = '请输入验证码'
      return
    }

    this.isLoading = true
    try {
      const loginData: LoginRequest = {
        phone: this.phoneNumber,
        code: this.verificationCode
      }

      const result = await AuthApi.login(loginData)
      if (result.success && result.data) {
        await authStore.login(result.data)
        this.successMessage = '登录成功'
        // 延迟跳转，让用户看到成功消息
        setTimeout(() => {
          router.replaceUrl({ url: 'pages/Index' })
        }, 1000)
      } else {
        this.errorMessage = result.message || '登录失败'
      }
    } catch (error) {
      this.errorMessage = '网络错误，请稍后重试'
    } finally {
      this.isLoading = false
    }
  }

  // 密码登录
  async loginWithPassword() {
    this.clearMessages()

    if (!this.validatePhone(this.phoneNumber)) {
      this.errorMessage = '请输入正确的手机号'
      return
    }

    if (!this.password) {
      this.errorMessage = '请输入密码'
      return
    }

    this.isLoading = true
    try {
      const loginData: LoginRequest = {
        phone: this.phoneNumber,
        password: this.password
      }

      const result = await AuthApi.login(loginData)
      if (result.success && result.data) {
        await authStore.login(result.data)
        this.successMessage = '登录成功'
        setTimeout(() => {
          router.replaceUrl({ url: 'pages/Index' })
        }, 1000)
      } else {
        this.errorMessage = result.message || '登录失败'
      }
    } catch (error) {
      this.errorMessage = '网络错误，请稍后重试'
    } finally {
      this.isLoading = false
    }
  }

  // 注册
  async register() {
    this.clearMessages()

    if (!this.userName.trim()) {
      this.errorMessage = '请输入用户名'
      return
    }

    if (!this.validatePhone(this.phoneNumber)) {
      this.errorMessage = '请输入正确的手机号'
      return
    }

    if (!this.password || this.password.length < 6) {
      this.errorMessage = '密码至少6位'
      return
    }

    this.isLoading = true
    try {
      const registerData: RegisterRequest = {
        name: this.userName,
        phone: this.phoneNumber,
        password: this.password,
        email: this.email
      }

      const result = await AuthApi.register(registerData)
      if (result.success && result.data) {
        await authStore.login(result.data)
        this.successMessage = '注册成功'
        setTimeout(() => {
          router.replaceUrl({ url: 'pages/Index' })
        }, 1000)
      } else {
        this.errorMessage = result.message || '注册失败'
      }
    } catch (error) {
      this.errorMessage = '网络错误，请稍后重试'
    } finally {
      this.isLoading = false
    }
  }

  build() {
    Column() {
      Image($r('app.media.phone2'))
        .width(50)
        .margin({ top: 30 })
      Text(this.title)
        .fontSize(25)
        .fontWeight(900)
        .margin({ top: 20, bottom: 5 })
      Text(this.message)
        .fontSize(14)
        .fontColor('#666')
        .margin({ bottom: 30 })

      // 错误和成功消息显示
      if (this.errorMessage) {
        Text(this.errorMessage)
          .fontSize(14)
          .fontColor('#ff4444')
          .margin({ bottom: 10 })
      }

      if (this.successMessage) {
        Text(this.successMessage)
          .fontSize(14)
          .fontColor('#00aa00')
          .margin({ bottom: 10 })
      }

      // 模式切换按钮
      Row() {
        Button(this.isRegisterMode ? '已有账号？去登录' : '没有账号？去注册')
          .buttonStyle(ButtonStyleMode.NORMAL)
          .fontSize(14)
          .onClick(() => {
            this.isRegisterMode = !this.isRegisterMode
            this.clearMessages()
          })
      }
      .width('100%')
      .justifyContent(FlexAlign.End)
      .margin({ bottom: 20 })

      // 注册表单
      if (this.isRegisterMode) Column() {
        TextInput({
          placeholder: '用户名',
          text: this.userName
        })
          .onChange((value: string) => {
            this.userName = value
          })
          .margin({ bottom: 15 })

        TextInput({
          placeholder: '手机号',
          text: this.phoneNumber
        })
          .maxLength(11)
          .type(InputType.Number)
          .onChange((value: string) => {
            this.phoneNumber = value
          })
          .margin({ bottom: 15 })

        TextInput({
          placeholder: '邮箱（可选）',
          text: this.email
        })
          .type(InputType.Email)
          .onChange((value: string) => {
            this.email = value
          })
          .margin({ bottom: 15 })

        TextInput({
          placeholder: '密码（至少6位）',
          text: this.password
        })
          .type(InputType.Password)
          .onChange((value: string) => {
            this.password = value
          })
          .margin({ bottom: 30 })

        Button('注册')
          .width('100%')
          .enabled(!this.isLoading)
          .onClick(() => {
            this.register()
          })
          .margin({ bottom: 15 })
      }
      // 短信验证码登录
      else if (this.isCodeLoginShow) Column() {
        Row() {
          Text('国家/地区')
            .fontColor('#666')
          Row() {
            Text(this.addressLabel)
              .fontColor('#666')
            Image($r('app.media.ic_public_arrow_right'))
              .width(20)
              .fillColor('#666')
          }
          .onClick(() => {
            router.pushUrl({
              url: 'pages/login/CountryRegion',
            })
          })
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)
        .padding({ left: 10, right: 10 })
        .margin({ bottom: 20 })

        TextInput({
          placeholder: '手机号',
          text: this.phoneNumber
        })
          .maxLength(11)
          .type(InputType.Number)
          .onChange((value: string) => {
            this.phoneNumber = value
          })
        Row({ space: 10 }) {
          TextInput({
            placeholder: '短信验证码',
            text: this.verificationCode
          })
            .width('60%')
            .type(InputType.Number)
            .onChange((value: string) => {
              this.verificationCode = value
            })
          Button(this.isCodeSent ? `${this.countdown}s` : '获取验证码')
            .buttonStyle(ButtonStyleMode.NORMAL)
            .enabled(!this.isCodeSent && !this.isLoading)
            .onClick(() => {
              this.sendCode()
            })
        }
        .margin({ top: 20, bottom: 30 })
        .width('100%')

        Button('登录')
          .width('100%')
          .enabled(!this.isLoading)
          .onClick(() => {
            this.loginWithCode()
          })
          .margin({ bottom: 15 })
        Text('密码登录')
          .fontColor('#007dff')
          .onClick(() => {
            this.isCodeLoginShow = false
          })
      }
      // 密码登录
      else if (!this.isCodeLoginShow) Column() {
        TextInput({
          placeholder: '手机号',
          text: this.phoneNumber
        })
          .maxLength(11)
          .type(InputType.Number)
          .onChange((value: string) => {
            this.phoneNumber = value
          })
        TextInput({
          placeholder: '密码',
          text: this.password
        })
          .type(InputType.Password)
          .onChange((value: string) => {
            this.password = value
          })
          .margin({ top: 20, bottom: 30 })
        Row() {
          Text('短信验证码登录')
            .fontColor('#007dff')
            .onClick(() => {
              this.isCodeLoginShow = true
            })
          Row() {
            Text('忘记密码')
              .fontColor('#007dff')
          }
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)
        .padding({ left: 10, right: 10 })
        .margin({ bottom: 25 })

        Button('登录')
          .width('100%')
          .enabled(!this.isLoading)
          .onClick(() => {
            this.loginWithPassword()
          })
          .margin({ bottom: 15 })
      }

      Blank()

      // 加载状态显示
      if (this.isLoading) {
        Row() {
          LoadingProgress()
            .width(20)
            .height(20)
            .margin({ right: 10 })
          Text('处理中...')
            .fontSize(14)
            .fontColor('#666')
        }
        .justifyContent(FlexAlign.Center)
        .margin({ bottom: 20 })
      }

      if (!this.isRegisterMode) {
        Text('其他方式登录')
          .fontColor('#666')
          .fontSize(14)
        Image($r('app.media.ic_public_comments'))
          .width(40)
          .border({
            width: 1,
            color: '#ccc'
          })
          .padding(10)
          .borderRadius(20)
          .margin({ top: 20, bottom: 20 })
      }

      Row() {
        Text('遇到问题').fontColor('#007dff').fontSize(14)
        Divider()
          .vertical(true)
          .height(20)
          .width(2)
          .color('#666')
          .margin({ left: 20, right: 20 })
        Text('用户协议').fontColor('#007dff').fontSize(14)
        Divider()
          .vertical(true)
          .height(20)
          .width(2)
          .color('#666')
          .margin({ left: 20, right: 20 })
        Text('隐私声明').fontColor('#007dff').fontSize(14)
      }
    }
    .width("100%")
    .height('100%')
    .padding(20)
  }
}
