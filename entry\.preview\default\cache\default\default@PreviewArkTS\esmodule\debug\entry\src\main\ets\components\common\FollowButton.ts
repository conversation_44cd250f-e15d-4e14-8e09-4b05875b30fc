if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface FollowButton_Params {
    isFollowed?: boolean;
}
import promptAction from "@ohos:promptAction";
export default class FollowButton extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__isFollowed = new ObservedPropertySimplePU(false, this, "isFollowed");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: FollowButton_Params) {
        if (params.isFollowed !== undefined) {
            this.isFollowed = params.isFollowed;
        }
    }
    updateStateVars(params: FollowButton_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__isFollowed.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__isFollowed.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __isFollowed: ObservedPropertySimplePU<boolean>;
    get isFollowed() {
        return this.__isFollowed.get();
    }
    set isFollowed(newValue: boolean) {
        this.__isFollowed.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.isFollowed ? '已关注' : '关注');
            Button.debugLine("entry/src/main/ets/components/common/FollowButton.ets(8:5)", "entry");
            Button.controlSize(ControlSize.SMALL);
            Button.buttonStyle(ButtonStyleMode.NORMAL);
            Button.fontColor(this.isFollowed ? '#cf0a2c' : '#000');
            Button.backgroundColor(this.isFollowed ? '#ffe3e3e3' : '#fff');
            Button.onClick(() => {
                this.isFollowed = !this.isFollowed;
                try {
                    promptAction.showToast({
                        message: this.isFollowed ? '已关注' : '已取消关注',
                        duration: 2000,
                        bottom: 80
                    });
                }
                catch (error) {
                    console.error(`showToast error: ${JSON.stringify(error)}`);
                }
            });
        }, Button);
        Button.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
