if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface HomeTab_Params {
    currentIndex?: number;
    controller?: TabsController;
    titles?: string[];
}
import GoodPick from "@bundle:com.example.harmonyhelloworld/entry/ets/components/common/GoodPick";
import HomeBanner from "@bundle:com.example.harmonyhelloworld/entry/ets/pages/home/<USER>/HomeBanner";
import HomeClassify from "@bundle:com.example.harmonyhelloworld/entry/ets/pages/home/<USER>/HomeClassify";
import HomeRecommend from "@bundle:com.example.harmonyhelloworld/entry/ets/pages/home/<USER>/HomeRecommend";
import HomeSubarea from "@bundle:com.example.harmonyhelloworld/entry/ets/pages/home/<USER>/HomeSubarea";
import HomeWelfare from "@bundle:com.example.harmonyhelloworld/entry/ets/pages/home/<USER>/HomeWelfare";
import { FooterComponent } from "@bundle:com.example.harmonyhelloworld/entry/ets/components/common/FooterComponent";
export class HomeTab extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__currentIndex = new ObservedPropertySimplePU(0, this, "currentIndex");
        this.controller = new TabsController();
        this.titles = ['推荐', '国家补贴', 'IQOO', 'VIVO'];
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: HomeTab_Params) {
        if (params.currentIndex !== undefined) {
            this.currentIndex = params.currentIndex;
        }
        if (params.controller !== undefined) {
            this.controller = params.controller;
        }
        if (params.titles !== undefined) {
            this.titles = params.titles;
        }
    }
    updateStateVars(params: HomeTab_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__currentIndex.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__currentIndex.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __currentIndex: ObservedPropertySimplePU<number>;
    get currentIndex() {
        return this.__currentIndex.get();
    }
    set currentIndex(newValue: number) {
        this.__currentIndex.set(newValue);
    }
    private controller: TabsController;
    private titles: string[];
    TabLabel(index: number, title: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/home/<USER>/HomeTab.ets(15:5)", "entry");
            Column.justifyContent(FlexAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/pages/home/<USER>/HomeTab.ets(16:7)", "entry");
            Text.fontColor(this.currentIndex === index ? '#000000' : '#656565');
            Text.fontSize(14);
            Text.padding({ top: 10, bottom: 10 });
        }, Text);
        Text.pop();
        Column.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/home/<USER>/HomeTab.ets(25:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.margin({
                top: 18,
                bottom: 12
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Tabs.create({
                barPosition: BarPosition.Start,
                controller: this.controller
            });
            Tabs.debugLine("entry/src/main/ets/pages/home/<USER>/HomeTab.ets(26:7)", "entry");
            Tabs.barMode(BarMode.Fixed);
            Tabs.barHeight(50);
            Tabs.onChange((index: number) => {
                this.currentIndex = index;
            });
            Tabs.width('100%');
            Tabs.flexGrow(1);
        }, Tabs);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const title = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    TabContent.create(() => {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Scroll.create();
                            Scroll.debugLine("entry/src/main/ets/pages/home/<USER>/HomeTab.ets(32:13)", "entry");
                            Scroll.scrollable(ScrollDirection.Vertical);
                            Scroll.width('100%');
                            Scroll.height('100%');
                        }, Scroll);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Column.create();
                            Column.debugLine("entry/src/main/ets/pages/home/<USER>/HomeTab.ets(33:15)", "entry");
                            Column.width('100%');
                            Column.backgroundColor(Color.White);
                        }, Column);
                        {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                if (isInitialRender) {
                                    let componentCall = new HomeBanner(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/home/<USER>/HomeTab.ets", line: 34, col: 17 });
                                    ViewPU.create(componentCall);
                                    let paramsLambda = () => {
                                        return {};
                                    };
                                    componentCall.paramsGenerator_ = paramsLambda;
                                }
                                else {
                                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                                }
                            }, { name: "HomeBanner" });
                        }
                        {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                if (isInitialRender) {
                                    let componentCall = new HomeClassify(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/home/<USER>/HomeTab.ets", line: 35, col: 17 });
                                    ViewPU.create(componentCall);
                                    let paramsLambda = () => {
                                        return {};
                                    };
                                    componentCall.paramsGenerator_ = paramsLambda;
                                }
                                else {
                                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                                }
                            }, { name: "HomeClassify" });
                        }
                        {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                if (isInitialRender) {
                                    let componentCall = new HomeWelfare(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/home/<USER>/HomeTab.ets", line: 36, col: 17 });
                                    ViewPU.create(componentCall);
                                    let paramsLambda = () => {
                                        return {};
                                    };
                                    componentCall.paramsGenerator_ = paramsLambda;
                                }
                                else {
                                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                                }
                            }, { name: "HomeWelfare" });
                        }
                        {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                if (isInitialRender) {
                                    let componentCall = new HomeRecommend(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/home/<USER>/HomeTab.ets", line: 37, col: 17 });
                                    ViewPU.create(componentCall);
                                    let paramsLambda = () => {
                                        return {};
                                    };
                                    componentCall.paramsGenerator_ = paramsLambda;
                                }
                                else {
                                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                                }
                            }, { name: "HomeRecommend" });
                        }
                        {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                if (isInitialRender) {
                                    let componentCall = new HomeSubarea(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/home/<USER>/HomeTab.ets", line: 38, col: 17 });
                                    ViewPU.create(componentCall);
                                    let paramsLambda = () => {
                                        return {};
                                    };
                                    componentCall.paramsGenerator_ = paramsLambda;
                                }
                                else {
                                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                                }
                            }, { name: "HomeSubarea" });
                        }
                        {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                if (isInitialRender) {
                                    let componentCall = new HomeSubarea(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/home/<USER>/HomeTab.ets", line: 39, col: 17 });
                                    ViewPU.create(componentCall);
                                    let paramsLambda = () => {
                                        return {};
                                    };
                                    componentCall.paramsGenerator_ = paramsLambda;
                                }
                                else {
                                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                                }
                            }, { name: "HomeSubarea" });
                        }
                        {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                if (isInitialRender) {
                                    let componentCall = new HomeSubarea(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/home/<USER>/HomeTab.ets", line: 40, col: 17 });
                                    ViewPU.create(componentCall);
                                    let paramsLambda = () => {
                                        return {};
                                    };
                                    componentCall.paramsGenerator_ = paramsLambda;
                                }
                                else {
                                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                                }
                            }, { name: "HomeSubarea" });
                        }
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            __Common__.create();
                            __Common__.margin({ left: 15, right: 15 });
                        }, __Common__);
                        {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                if (isInitialRender) {
                                    let componentCall = new GoodPick(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/home/<USER>/HomeTab.ets", line: 41, col: 17 });
                                    ViewPU.create(componentCall);
                                    let paramsLambda = () => {
                                        return {};
                                    };
                                    componentCall.paramsGenerator_ = paramsLambda;
                                }
                                else {
                                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                                }
                            }, { name: "GoodPick" });
                        }
                        __Common__.pop();
                        {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                if (isInitialRender) {
                                    let componentCall = new FooterComponent(this, {
                                        companyName: '我的商城',
                                        recordNumber: '24215150227'
                                    }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/home/<USER>/HomeTab.ets", line: 43, col: 17 });
                                    ViewPU.create(componentCall);
                                    let paramsLambda = () => {
                                        return {
                                            companyName: '我的商城',
                                            recordNumber: '24215150227'
                                        };
                                    };
                                    componentCall.paramsGenerator_ = paramsLambda;
                                }
                                else {
                                    this.updateStateVarsOfChildByElmtId(elmtId, {});
                                }
                            }, { name: "FooterComponent" });
                        }
                        Column.pop();
                        Scroll.pop();
                    });
                    TabContent.tabBar({ builder: () => {
                            this.TabLabel.call(this, index, title);
                        } });
                    TabContent.debugLine("entry/src/main/ets/pages/home/<USER>/HomeTab.ets(31:11)", "entry");
                }, TabContent);
                TabContent.pop();
            };
            this.forEachUpdateFunction(elmtId, this.titles, forEachItemGenFunction, (item: string, index: number) => index.toString(), true, true);
        }, ForEach);
        ForEach.pop();
        Tabs.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
export default HomeTab;
