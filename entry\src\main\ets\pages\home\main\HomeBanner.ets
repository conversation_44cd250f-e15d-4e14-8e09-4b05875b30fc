@Component
export struct HomeBanner {

  build() {
    Swiper(){
      Image($r('app.media.banner1'))
        .objectFit(ImageFit.Contain)
        .width('100%')
      Image($r('app.media.banner2'))
        .objectFit(ImageFit.Contain)
        .width('100%')
      Image($r('app.media.banner3'))
        .objectFit(ImageFit.Contain)
        .width('100%')
      Image($r('app.media.banner4'))
        .objectFit(ImageFit.Contain)
        .width('100%')
      Image($r('app.media.banner5'))
        .objectFit(ImageFit.Contain)
        .width('100%')
    }
    .margin({
      left: 12,
      right: 12
    })
    .borderRadius(10)
    .autoPlay(true)
    .indicator(
      Indicator.dot()
        .selectedColor(Color.White)
    )
  }
}

export default HomeBanner

