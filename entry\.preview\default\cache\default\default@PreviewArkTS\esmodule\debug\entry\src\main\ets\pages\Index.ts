if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface Index_Params {
    currentIndex?: number;
    menus?: MenuItemType[];
}
import Activity from "@bundle:com.example.harmonyhelloworld/entry/ets/pages/activity/Activity";
import Classify from "@bundle:com.example.harmonyhelloworld/entry/ets/pages/category/Classify";
import Home from "@bundle:com.example.harmonyhelloworld/entry/ets/pages/home/<USER>";
import ShoppingCart from "@bundle:com.example.harmonyhelloworld/entry/ets/pages/shoppingCart/ShoppingCart";
import UserCenter from "@bundle:com.example.harmonyhelloworld/entry/ets/pages/userCenter/UserCenter";
import font from "@ohos:font";
import router from "@ohos:router";
interface MenuItemType {
    icon: string;
    text: string;
}
interface ParamsType {
    index: number;
}
class Index extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__currentIndex = new ObservedPropertySimplePU(0, this, "currentIndex");
        this.__menus = new ObservedPropertyObjectPU([
            { icon: 'ic_public_home_filled', text: '首页' },
            { icon: 'ic_public_view_grid', text: '分页' },
            { icon: 'ic_public_timer', text: '发现' },
            { icon: 'ic_public_appstore', text: '购物车' },
            { icon: 'ic_public_contacts', text: '我的' },
        ], this, "menus");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: Index_Params) {
        if (params.currentIndex !== undefined) {
            this.currentIndex = params.currentIndex;
        }
        if (params.menus !== undefined) {
            this.menus = params.menus;
        }
    }
    updateStateVars(params: Index_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__currentIndex.purgeDependencyOnElmtId(rmElmtId);
        this.__menus.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__currentIndex.aboutToBeDeleted();
        this.__menus.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __currentIndex: ObservedPropertySimplePU<number>;
    get currentIndex() {
        return this.__currentIndex.get();
    }
    set currentIndex(newValue: number) {
        this.__currentIndex.set(newValue);
    }
    private __menus: ObservedPropertyObjectPU<MenuItemType[]>;
    get menus() {
        return this.__menus.get();
    }
    set menus(newValue: MenuItemType[]) {
        this.__menus.set(newValue);
    }
    aboutToAppear(): void {
        // 页面加载就注册iconfont
        font.registerFont({
            familyName: 'sysFont',
            familySrc: '/utils/font/iconfont.ttf'
        });
        // 功能直达进入页面
        this.currentIndex = (router.getParams() as ParamsType)?.index || 0;
    }
    // 菜单样式
    TabBarBuilder(item: MenuItemType, index: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 4 });
            Column.debugLine("entry/src/main/ets/pages/Index.ets(46:5)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": -1, "type": -1, params: [`app.media.${item.icon}`], "bundleName": "com.example.harmonyhelloworld", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/Index.ets(47:7)", "entry");
            Image.width(25);
            Image.fillColor(index === this.currentIndex ? '#ca141d' : '#808182');
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.text);
            Text.debugLine("entry/src/main/ets/pages/Index.ets(50:7)", "entry");
            Text.fontSize(12);
            Text.fontColor(index === this.currentIndex ? '#ca141d' : '#808182');
        }, Text);
        Text.pop();
        Column.pop();
    }
    // 加载菜单对应的组件
    TabEachBuilder(index: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (index === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new Home(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/Index.ets", line: 60, col: 7 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {};
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "Home" });
                    }
                });
            }
            else if (index === 1) {
                this.ifElseBranchUpdateFunction(1, () => {
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new Classify(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/Index.ets", line: 62, col: 7 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {};
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "Classify" });
                    }
                });
            }
            else if (index === 2) {
                this.ifElseBranchUpdateFunction(2, () => {
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new Activity(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/Index.ets", line: 64, col: 7 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {};
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "Activity" });
                    }
                });
            }
            else if (index === 3) {
                this.ifElseBranchUpdateFunction(3, () => {
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new ShoppingCart(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/Index.ets", line: 66, col: 7 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {};
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "ShoppingCart" });
                    }
                });
            }
            else {
                this.ifElseBranchUpdateFunction(4, () => {
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new UserCenter(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/Index.ets", line: 68, col: 7 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {};
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "UserCenter" });
                    }
                });
            }
        }, If);
        If.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/Index.ets(73:5)", "entry");
            Column.height('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Tabs.create({ barPosition: BarPosition.End, index: this.currentIndex });
            Tabs.debugLine("entry/src/main/ets/pages/Index.ets(75:7)", "entry");
            Tabs.scrollable(true);
            Tabs.animationDuration(0);
            Tabs.barBackgroundColor({ "id": 16777270, "type": 10001, params: [], "bundleName": "com.example.harmonyhelloworld", "moduleName": "entry" });
            Tabs.onChange((index: number) => {
                this.currentIndex = index;
            });
            Tabs.divider({
                strokeWidth: 1,
                color: '#ffeaeaea'
            });
        }, Tabs);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const item = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    TabContent.create(() => {
                        this.TabEachBuilder.bind(this)(index);
                    });
                    TabContent.tabBar({ builder: () => {
                            this.TabBarBuilder.call(this, item, index);
                        } });
                    TabContent.debugLine("entry/src/main/ets/pages/Index.ets(77:11)", "entry");
                }, TabContent);
                TabContent.pop();
            };
            this.forEachUpdateFunction(elmtId, this.menus, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        Tabs.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "Index";
    }
}
registerNamedRoute(() => new Index(undefined, {}), "", { bundleName: "com.example.harmonyhelloworld", moduleName: "entry", pagePath: "pages/Index", pageFullPath: "entry/src/main/ets/pages/Index", integratedHsp: "false", moduleType: "followWithHap" });
