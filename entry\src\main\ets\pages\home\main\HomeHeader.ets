import { router } from '@kit.ArkUI'
import IndexApi from '../../../api/home';
import { SEARCH_TIP } from '../../../utils/constant';

@Component
struct HomeHeader {
  @State tipList: string[] = []
  @State findTipsCount: number = 0
  private timer: number = -1

  // 组件创建后
  async aboutToAppear(): Promise<void> {
    // 请求输入框的提示文案列表
    await this.getTipList()
    // 开启定时器用于切换提示文案
    this.findTipsCountChange()
  }

  async getTipList(): Promise<void> {
    this.tipList = await IndexApi.getTipList()
  }

  findTipsCountChange(): void {
    clearInterval(this.timer)
    this.timer = setInterval(() => {
      if (this.findTipsCount >= this.tipList.length - 1) {
        this.findTipsCount = 0
      } else {
        this.findTipsCount++
      }
    }, 3000)
  }

  // 组件销毁
  aboutToDisappear(): void {
    clearInterval(this.timer)
  }

  build() {
    Row({ space: 10 }) {
      // 搜索框
      Row() {
        Image($r('app.media.search'))
          .width(24)
        Text(this.tipList[this.findTipsCount])
          .backgroundColor(Color.Transparent)
          .fontColor($r('app.color.gray_font'))
          .textOverflow({
            overflow: TextOverflow.Ellipsis
          })
          .maxLines(1)
          .layoutWeight(1)
      }
      .backgroundColor('#f5f4f1')
      .borderRadius(25)
      .layoutWeight(1)
      .padding(10)
      .onClick(() => {
        AppStorage.setOrCreate(SEARCH_TIP, this.tipList[this.findTipsCount])
        router.pushUrl({
          url: 'pages/search/SearchPage'
        })
      })
      Image($r('app.media.shopcart'))
        .width(28)
      Image($r('app.media.comments'))
        .width(28)

    }
    .width('100%')
    .height(58)
    .backgroundColor('#f1f35')
    .padding({
      left: 10,
      right: 10
    })
  }
}

export default HomeHeader