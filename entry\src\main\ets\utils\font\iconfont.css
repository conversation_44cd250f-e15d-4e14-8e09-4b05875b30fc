@font-face {
  font-family: "iconfont"; /* Project id 4728475 */
  src: url('iconfont.woff2?t=1733198834925') format('woff2'),
       url('iconfont.woff?t=1733198834925') format('woff'),
       url('iconfont.ttf?t=1733198834925') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-close:before {
  content: "\e604";
}

.icon-arrow-right:before {
  content: "\e665";
}

.icon-gengduo:before {
  content: "\e61e";
}

.icon-kefu:before {
  content: "\ec2e";
}

.icon-gouwuche:before {
  content: "\e67e";
}

.icon-shouye-:before {
  content: "\e607";
}

.icon-play_fill:before {
  content: "\e717";
}

.icon-arrow-right-filling:before {
  content: "\e68a";
}

.icon-menu:before {
  content: "\e677";
}

.icon-modular:before {
  content: "\e678";
}

.icon-arrow-up-filling:before {
  content: "\e688";
}

.icon-arrow-down-filling:before {
  content: "\e689";
}

