import { User } from '../models/User';
import preferences from '@ohos.data.preferences';

// 用户认证状态管理
export class AuthStore {
  private static instance: AuthStore;
  private _isLoggedIn: boolean = false;
  private _currentUser: User | null = null;
  private _token: string | null = null;

  // 状态变化监听器
  private listeners: Array<() => void> = [];

  private constructor() {
    this.loadUserFromStorage();
  }

  // 单例模式
  static getInstance(): AuthStore {
    if (!AuthStore.instance) {
      AuthStore.instance = new AuthStore();
    }
    return AuthStore.instance;
  }

  // 获取登录状态
  get isLoggedIn(): boolean {
    return this._isLoggedIn;
  }

  // 获取当前用户
  get currentUser(): User | null {
    return this._currentUser;
  }

  // 获取token
  get token(): string | null {
    return this._token;
  }

  // 登录
  async login(user: User, token?: string): Promise<void> {
    this._isLoggedIn = true;
    this._currentUser = user;
    this._token = token || 'mock_token_' + Date.now();

    // 保存到本地存储
    await this.saveUserToStorage();

    // 通知监听器
    this.notifyListeners();
  }

  // 登出
  async logout(): Promise<void> {
    this._isLoggedIn = false;
    this._currentUser = null;
    this._token = null;

    // 清除本地存储
    await this.clearUserFromStorage();

    // 通知监听器
    this.notifyListeners();
  }

  // 更新用户信息
  async updateUser(user: User): Promise<void> {
    this._currentUser = user;
    await this.saveUserToStorage();
    this.notifyListeners();
  }

  // 添加状态变化监听器
  addListener(listener: () => void): void {
    this.listeners.push(listener);
  }

  // 移除状态变化监听器
  removeListener(listener: () => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  // 通知所有监听器
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener());
  }

  // 从本地存储加载用户信息
  private async loadUserFromStorage(): Promise<void> {
    try {
      const context = getContext();
      const dataPreferences = preferences.getPreferences(context, 'user_prefs');

      const isLoggedIn = await (await dataPreferences).get('isLoggedIn', false) as boolean;
      const userStr = await (await dataPreferences).get('currentUser', '') as string;
      const token = await (await dataPreferences).get('token', '') as string;

      if (isLoggedIn && userStr) {
        this._isLoggedIn = true;
        this._currentUser = JSON.parse(userStr) as User;
        this._token = token;
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  }

  // 保存用户信息到本地存储
  private async saveUserToStorage(): Promise<void> {
    try {
      const context = getContext();
      const dataPreferences = preferences.getPreferences(context, 'user_prefs');

      await (await dataPreferences).put('isLoggedIn', this._isLoggedIn);
      await (await dataPreferences).put('currentUser', JSON.stringify(this._currentUser));
      await (await dataPreferences).put('token', this._token || '');
      await (await dataPreferences).flush();
    } catch (error) {
      console.error('保存用户信息失败:', error);
    }
  }

  // 清除本地存储的用户信息
  private async clearUserFromStorage(): Promise<void> {
    try {
      const context = getContext();
      const dataPreferences = preferences.getPreferences(context, 'user_prefs');

      await (await dataPreferences).delete('isLoggedIn');
      await (await dataPreferences).delete('currentUser');
      await (await dataPreferences).delete('token');
      await (await dataPreferences).flush();
    } catch (error) {
      console.error('清除用户信息失败:', error);
    }
  }
}

// 导出单例实例
export const authStore = AuthStore.getInstance();