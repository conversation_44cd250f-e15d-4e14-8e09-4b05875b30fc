import GoodPick from "../../../components/common/GoodPick";
import HomeBanner from "./HomeBanner";
import HomeClassify from "./HomeClassify";
import HomeRecommend from "./HomeRecommend";
import HomeSubarea from "./HomeSubarea";
import HomeWelfare from "./HomeWelfare";
import { FooterComponent } from '../../../components/common/FooterComponent'
@Component
export struct HomeTab {
  @State currentIndex: number = 0;
  private controller: TabsController = new TabsController();
  private titles: string[] = ['推荐', '国家补贴', 'IQOO', 'VIVO'];

  @Builder TabLabel(index: number, title: string) {
    Column() {
      Text(title)
        .fontColor(this.currentIndex === index ? '#000000' : '#656565')
        .fontSize(14)
        .padding({ top: 10, bottom: 10 })
    }
    .justifyContent(FlexAlign.Center)
  }

  build() {
    Column() {
      Tabs({
        barPosition: BarPosition.Start,
        controller: this.controller
      }) {
        ForEach(this.titles, (title: string, index: number) => {
          TabContent() {
            Scroll() {
              Column() {
                HomeBanner()
                HomeClassify()
                HomeWelfare()
                HomeRecommend()
                HomeSubarea()
                HomeSubarea()
                HomeSubarea()
                GoodPick()
                  .margin({ left: 15, right: 15 })
                FooterComponent({
                  companyName: '我的商城',
                  recordNumber: '24215150227'
                })
              }
              .width('100%')
              .backgroundColor(Color.White)
            }
            .scrollable(ScrollDirection.Vertical)
            .width('100%')
            .height('100%')
          }
          .tabBar(this.TabLabel(index, title))
        }, (item: string, index: number) => index.toString())
      }
      .barMode(BarMode.Fixed)
      .barHeight(50)
      .onChange((index: number) => {
        this.currentIndex = index;
      })
      .width('100%')
      .flexGrow(1)
    }
    .width('100%')
    .height('100%')
    .margin({
      top: 18,
      bottom: 12
    })
  }
}

export default HomeTab