@Component
export struct HomeSubarea {
  build() {
    Column() {
      Column() {
        Row() {
          Text('手机精选')
            .fontSize(18)
            .fontWeight(700)
          Blank()
          Row() {
            Text('更多')
              .fontSize(14)
              .fontColor('#ff9b9a9a')
            Image($r('app.media.ic_public_arrow_right'))
              .width(18)
              .fillColor('#ff9b9a9a')
          }
        }
        .width('100%')

        Flex({
          wrap: FlexWrap.Wrap
        }) {
          Column() {
            Image('https://shopstatic.vivo.com.cn/vivoshop/commodity/commodity/10010962_1747212911205_750x750.png.webp')
              .width(120)
            Text('iQOO Pad5 Pro')
              .fontSize(14)
              .fontWeight(600)
              .margin(6)

            Text('￥3199')
              .fontSize(14)
              .fontWeight(600)
          }
          .width('50%')

          Column() {
            Image('https://shopstatic.vivo.com.cn/vivoshop/commodity/commodity/10010550_1735639603201_750x750.png.webp')
              .width(120)
            Text('iQOO Z9')
              .fontSize(14)
              .fontWeight(600)
              .margin(6)

            Text('￥1699')
              .fontSize(14)
              .fontWeight(600)
          }
          .width('50%')
        }
      }
      .width('100%')
      .backgroundColor('#fff')
      .borderRadius(15)
      .padding(15)
      .alignItems(HorizontalAlign.Start)
    }
    .width('100%')
    .padding({
      left: 12,
      right: 12
    })
    .margin({
      top: 8
    })
  }
}

export default HomeSubarea

