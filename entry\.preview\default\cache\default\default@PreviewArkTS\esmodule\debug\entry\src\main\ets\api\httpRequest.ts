import axios from "@package:pkg_modules/.ohpm/@ohos+axios@2.2.2/pkg_modules/@ohos/axios/index";
import type { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from "@package:pkg_modules/.ohpm/@ohos+axios@2.2.2/pkg_modules/@ohos/axios/index";
// 修改baseURL为空，因为我们会提供完整URL
const service = axios.create({
    baseURL: '',
    timeout: 10 * 60 * 1000
});
// 请求拦截器
service.interceptors.request.use((config: InternalAxiosRequestConfig) => {
    // 可以在这里添加token等认证信息
    return config;
}, (error: AxiosError) => {
    return Promise.reject(error.response);
});
// 响应拦截器
service.interceptors.response.use((response: AxiosResponse): AxiosResponse => {
    return response.data;
}, (error: AxiosError) => {
    return Promise.reject(error.response);
});
interface configType {
    params?: object;
}
interface RequestArgumentType {
    url: string;
    method: string;
    data?: object;
    config?: configType;
}
interface AxiosConfigType {
    get: (url: string, params: object) => Promise<AxiosResponse>;
    post: (url: string, data: object, config: configType) => Promise<AxiosResponse>;
    put: (url: string, data: object, config: configType) => Promise<AxiosResponse>;
    delete: (url: string, data: object) => Promise<AxiosResponse>;
}
// 请求类型配置
const axiosConfig: AxiosConfigType = {
    get: (url: string, params: object) => {
        console.log('2222', service.get(url, { params }));
        return service.get(url, { params });
    },
    post: (url: string, data: object, config: configType) => {
        return service.post(url, data, {
            headers: {
                'Content-Type': 'application/json'
            },
            params: config?.params
        });
    },
    put: (url: string, data: object, config: configType) => {
        return service.put(url, data, {
            params: config?.params
        });
    },
    delete: (url: string, data: object) => {
        return service.delete(url, { params: data });
    }
};
// 请求入口
const httpRequest = (data: RequestArgumentType): Promise<AxiosResponse> => {
    return Object(axiosConfig)[data.method](data.url, data?.data || {}, data?.config || {});
};
export default httpRequest;
