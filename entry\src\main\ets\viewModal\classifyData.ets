
export class CategoryType {
  "actionUrlWap": string
  "actionUrlWeb": string
  "categoryId": number
  "categoryName": string
  "categoryParentId"?: number
  "categoryType": string
  "configInfo": string
  "dataSourceCode": string
  "dataType"?: string
  "moreActionUrl"?: string
  "moreUrlType": string
  "newTabFlag": string
  "orderNum": number
  "subCategoryList"?: CategoryType[]
}

export const categoryList: CategoryType[] = [
  {
    "actionUrlWap": "",
    "actionUrlWeb": "",
    "categoryId": 29693,
    "categoryName": "推荐",
    "categoryType": "1",
    "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045516\", \"dataSourceType\": \"prod\"}]}",
    "dataSourceCode": "DT000515",
    "dataType": "5",
    "moreUrlType": "1",
    "newTabFlag": "0",
    "orderNum": 13,
    "subCategoryList": [
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 30901,
        "categoryName": "IQOO手机榜",
        "categoryParentId": 29693,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA050256\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 2
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29717,
        "categoryName": "好物销售榜",
        "categoryParentId": 29693,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045359\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 10
      }
    ]
  },
  {
    "actionUrlWap": "",
    "actionUrlWeb": "",
    "categoryId": 29694,
    "categoryName": "VIVO",
    "categoryType": "1",
    "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045517\", \"dataSourceType\": \"prod\"}]}",
    "dataSourceCode": "DT000515",
    "dataType": "5",
    "moreUrlType": "1",
    "newTabFlag": "0",
    "orderNum": 14,
    "subCategoryList": [
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29725,
        "categoryName": "爆款推荐",
        "categoryParentId": 29694,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045367\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 1
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29726,
        "categoryName": "IQOo系列",
        "categoryParentId": 29694,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045368\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 2
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29727,
        "categoryName": "X系列",
        "categoryParentId": 29694,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045369\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 3
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29728,
        "categoryName": "S系列",
        "categoryParentId": 29694,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045370\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 4
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29729,
        "categoryName": "Z系列",
        "categoryParentId": 29694,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045371\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 5
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29730,
        "categoryName": "Y系列",
        "categoryParentId": 29694,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045372\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 6
      }

    ]
  },
  {
    "actionUrlWap": "",
    "actionUrlWeb": "",
    "categoryId": 29695,
    "categoryName": "S系列",
    "categoryType": "1",
    "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045518\", \"dataSourceType\": \"prod\"}]}",
    "dataSourceCode": "DT000515",
    "dataType": "5",
    "moreUrlType": "1",
    "newTabFlag": "0",
    "orderNum": 15,
    "subCategoryList": [
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29735,
        "categoryName": "爆款推荐",
        "categoryParentId": 29695,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045377\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 1
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29736,
        "categoryName": "A系列",
        "categoryParentId": 29695,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045378\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 2
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29737,
        "categoryName": "B系列",
        "categoryParentId": 29695,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045379\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 3
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29738,
        "categoryName": "C系列",
        "categoryParentId": 29695,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045380\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 4
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29739,
        "categoryName": "手表/手环",
        "categoryParentId": 29695,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045381\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 5
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29740,
        "categoryName": "健康及儿童手表",
        "categoryParentId": 29695,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045382\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 6
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29741,
        "categoryName": "智能观影眼镜",
        "categoryParentId": 29695,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045383\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 7
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29742,
        "categoryName": "配件周边",
        "categoryParentId": 29695,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045384\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 8
      }
    ]
  },
  {
    "actionUrlWap": "",
    "actionUrlWeb": "",
    "categoryId": 29696,
    "categoryName": "平板",
    "categoryType": "1",
    "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045519\", \"dataSourceType\": \"prod\"}]}",
    "dataSourceCode": "DT000515",
    "dataType": "5",
    "moreUrlType": "1",
    "newTabFlag": "0",
    "orderNum": 16,
    "subCategoryList": [
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29743,
        "categoryName": "爆款推荐",
        "categoryParentId": 29696,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045385\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 1
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 30819,
        "categoryName": "VIVO平板",
        "categoryParentId": 29696,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA049028\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 5
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29745,
        "categoryName": "IQOO系列",
        "categoryParentId": 29696,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045387\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 6
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29746,
        "categoryName": "平板配件",
        "categoryParentId": 29696,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045388\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 7
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29747,
        "categoryName": "智能手表",
        "categoryParentId": 29696,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045389\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 8
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29749,
        "categoryName": "配件周边",
        "categoryParentId": 29696,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045391\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 10
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 30822,
        "categoryName": "配件周边",
        "categoryParentId": 29696,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA049178\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 11
      }
    ]
  },
  {
    "actionUrlWap": "",
    "actionUrlWeb": "",
    "categoryId": 29697,
    "categoryName": "笔记本",
    "categoryType": "1",
    "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045520\", \"dataSourceType\": \"prod\"}]}",
    "dataSourceCode": "DT000515",
    "dataType": "5",
    "moreUrlType": "1",
    "newTabFlag": "0",
    "orderNum": 17,
    "subCategoryList": [
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29750,
        "categoryName": "爆款推荐",
        "categoryParentId": 29697,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045392\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 1
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29751,
        "categoryName": "A系列",
        "categoryParentId": 29697,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045393\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 2
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29752,
        "categoryName": "A系列",
        "categoryParentId": 29697,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045394\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 3
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29753,
        "categoryName": "A系列",
        "categoryParentId": 29697,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045395\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 4
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29754,
        "categoryName": "A系列",
        "categoryParentId": 29697,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045396\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 5
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29755,
        "categoryName": "A系列",
        "categoryParentId": 29697,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045397\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 6
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29756,
        "categoryName": "A系列",
        "categoryParentId": 29697,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045398\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 7
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29757,
        "categoryName": "配件周边",
        "categoryParentId": 29697,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045399\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 8
      }
    ]
  },
  {
    "actionUrlWap": "",
    "actionUrlWeb": "",
    "categoryId": 29698,
    "categoryName": "台显打印",
    "categoryType": "1",
    "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045521\", \"dataSourceType\": \"prod\"}]}",
    "dataSourceCode": "DT000515",
    "dataType": "5",
    "moreUrlType": "1",
    "newTabFlag": "0",
    "orderNum": 18,
    "subCategoryList": [
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29758,
        "categoryName": "爆款推荐",
        "categoryParentId": 29698,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045400\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 1
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29759,
        "categoryName": "台式机和一体机",
        "categoryParentId": 29698,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045401\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 2
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 30243,
        "categoryName": "显示器",
        "categoryParentId": 29698,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA046616\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "0",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 4
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29761,
        "categoryName": "打印机",
        "categoryParentId": 29698,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045403\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 5
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29762,
        "categoryName": "配件周边",
        "categoryParentId": 29698,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045404\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 6
      }
    ]
  },
  {
    "actionUrlWap": "",
    "actionUrlWeb": "",
    "categoryId": 29699,
    "categoryName": "耳机音箱",
    "categoryType": "1",
    "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045522\", \"dataSourceType\": \"prod\"}]}",
    "dataSourceCode": "DT000515",
    "dataType": "5",
    "moreUrlType": "1",
    "newTabFlag": "0",
    "orderNum": 19,
    "subCategoryList": [
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29763,
        "categoryName": "爆款推荐",
        "categoryParentId": 29699,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045405\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 1
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29764,
        "categoryName": "A系列",
        "categoryParentId": 29699,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045406\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 2
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 30628,
        "categoryName": "A系列",
        "categoryParentId": 29699,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA047894\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 3
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29768,
        "categoryName": "A系列",
        "categoryParentId": 29699,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045410\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 4
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 30627,
        "categoryName": "A系列",
        "categoryParentId": 29699,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA047893\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 5
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29765,
        "categoryName": "A系列",
        "categoryParentId": 29699,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045407\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 6
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29766,
        "categoryName": "有线耳机",
        "categoryParentId": 29699,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045408\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 7
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29767,
        "categoryName": "智能音箱",
        "categoryParentId": 29699,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045409\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 8
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29769,
        "categoryName": "配件周边",
        "categoryParentId": 29699,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045411\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 9
      }
    ]
  },
  {
    "actionUrlWap": "https://msale.vmall.com/hwzhp.html",
    "actionUrlWeb": "",
    "categoryId": 29700,
    "categoryName": "智慧屏",
    "categoryType": "1",
    "configInfo": "{\"dataSourceList\": [{\"dataSourceCode\": \"DA045418\", \"dataSourceType\": \"prod\"}]}",
    "dataSourceCode": "DT000515",
    "moreActionUrl": "https://asale.vmall.com/hwzhp.html",
    "moreUrlType": "3",
    "newTabFlag": "1",
    "orderNum": 20,
    "subCategoryList": [
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29770,
        "categoryName": "爆款推荐",
        "categoryParentId": 29700,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045442\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "0",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 24
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29771,
        "categoryName": "智慧屏 V系列 旗舰音画",
        "categoryParentId": 29700,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045443\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "moreActionUrl": "",
        "moreUrlType": "3",
        "newTabFlag": "1",
        "orderNum": 28
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29772,
        "categoryName": "智慧屏 S系列 智慧娱乐",
        "categoryParentId": 29700,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045444\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "moreActionUrl": "",
        "moreUrlType": "3",
        "newTabFlag": "1",
        "orderNum": 29
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29773,
        "categoryName": "Vision智慧屏 智慧先锋",
        "categoryParentId": 29700,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045445\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "0",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 30
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29775,
        "categoryName": "配件周边",
        "categoryParentId": 29700,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045447\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 38
      }
    ]
  },
  {
    "actionUrlWap": "",
    "actionUrlWeb": "",
    "categoryId": 29701,
    "categoryName": "智能家居",
    "categoryType": "1",
    "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045523\", \"dataSourceType\": \"prod\"}]}",
    "dataSourceCode": "DT000515",
    "dataType": "5",
    "moreUrlType": "1",
    "newTabFlag": "0",
    "orderNum": 22,
    "subCategoryList": [
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29776,
        "categoryName": "爆款推荐",
        "categoryParentId": 29701,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045412\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 1
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29777,
        "categoryName": "智能门锁",
        "categoryParentId": 29701,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045416\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "0",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 4
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29778,
        "categoryName": "家庭存储",
        "categoryParentId": 29701,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045417\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "0",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 5
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29779,
        "categoryName": "智能路由",
        "categoryParentId": 29701,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045413\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 6
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29780,
        "categoryName": "移动路由",
        "categoryParentId": 29701,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045414\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 7
      },
      {
        "actionUrlWap": "",
        "actionUrlWeb": "",
        "categoryId": 29781,
        "categoryName": "配件周边",
        "categoryParentId": 29701,
        "categoryType": "2",
        "configInfo": "{\"sceneCode\": \"\", \"sceneName\": \"\", \"dataSourceList\": [{\"dataSourceCode\": \"DA045415\", \"dataSourceType\": \"prod\"}]}",
        "dataSourceCode": "DT000515",
        "dataType": "5",
        "moreUrlType": "1",
        "newTabFlag": "0",
        "orderNum": 8
      }
    ]
  }


]
