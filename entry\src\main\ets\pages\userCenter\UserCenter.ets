import GoodPick from '../../components/common/GoodPick'
import { router } from '@kit.ArkUI'

interface StatItem {
  value: string;
  label: string;
  isIcon?: boolean;
  icon?: Resource;
}

interface GridItemType {
  icon: Resource;
  label: string;
}

@Component
struct UserCenter {
  @State isLoggedIn: boolean = false;
  private stats: StatItem[] = [
    { value: '4', label: '收藏' },
    { value: '3', label: '优惠券' },
    { value: '453', label: '积分' },
    { value: '', label: '钱包', isIcon: true, icon: $r('app.media.bag') }
  ];

  private orderItems: GridItemType[] = [
    { icon: $r('app.media.setting'), label: '待付款' },
    { icon: $r('app.media.setting'), label: '待发货' },
    { icon: $r('app.media.comments'), label: '待收货' },
    { icon: $r('app.media.comments'), label: '待评价' },
    { icon: $r('app.media.setting'), label: '退款/售后' }
  ];

  private serviceItems: GridItemType[] = [
    { icon: $r('app.media.comments'), label: '价格保护' },
    { icon: $r('app.media.setting'), label: '分期支付' },
    { icon: $r('app.media.comments'), label: '以旧换新' },
    { icon: $r('app.media.setting'),label: '定制服务' },
    { icon: $r('app.media.setting'), label: '退款/售后' }
  ];

  // --- 顶部栏构建器 ---
  @Builder
  TopBar() {
    Row() {
      Text('我的')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
      Blank()
      Row({ space: 22 }) {
        Stack({ alignContent: Alignment.TopEnd }) {
          Image($r('app.media.setting'))
            .width(24).height(24)
          Circle().width(6).height(6).fill(Color.Red).offset({ x: 2, y: -2 })
        }
        Image($r('app.media.comments')) // 用 comments 作为购物车图标占位符
          .width(24).height(24)
        Stack({ alignContent: Alignment.TopEnd }) {
          Image($r('app.media.comments')) // 使用您原有的评论图标
            .width(24).height(24)
          Circle().width(6).height(6).fill(Color.Red).offset({ x: 2, y: -2 })
        }
      }
    }
    .width('100%')
    .height(48)
    .alignItems(VerticalAlign.Center)

  }

  // --- 用户信息构建器 ---
  // @Builder
  // UserProfile() {
  //   Row({ space: 12 }) {
  //     Image($r('app.media.pic2')) // 使用您原有的头像图标
  //       .width(64).height(64).borderRadius(32)
  //
  //     Column({ space: 8 }) {
  //       Text('浮生若梦')
  //         .fontSize(18)
  //         .fontWeight(FontWeight.Bold)
  //       Text('vivo账号: 135****0102')
  //         .fontSize(13)
  //         .fontColor(Color.Gray)
  //       Row({ space: 4 }) {
  //         Image($r('app.media.setting'))
  //           .width(16).height(16)
  //         Text('黄金会员')
  //           .fontSize(12)
  //           .fontColor('#8c5a00')
  //       }
  //       .backgroundColor('#fff0c8')
  //       .borderRadius(4)
  //     }
  //     .alignItems(HorizontalAlign.Start)
  //   }
  //   .width('100%')
  //   .padding({ left: 16, top: 10 })
  // }
  @Builder
  UserProfile() {
    // 根据 isLoggedIn 的值，决定显示哪个视图
    if (this.isLoggedIn) {
      // --- 已登录视图 ---
      Row({ space: 12 }) {
        Image($r('app.media.pic2'))
          .width(64).height(64).borderRadius(32)

        Column({ space: 8 }) {
          Text('浮生若梦')
            .fontSize(18)
            .fontWeight(FontWeight.Bold)
          Text('vivo账号: 180****7506')
            .fontSize(13)
            .fontColor(Color.Gray)
          Row({ space: 4 }) {
            Image($r('app.media.setting'))
              .width(16).height(16)
            Text('黄金会员')
              .fontSize(12)
              .fontColor('#8c5a00')
          }
          .backgroundColor('#fff0c8')
          .borderRadius(4)
        }
        .alignItems(HorizontalAlign.Start)
      }
      .width('100%')
      .padding({ left: 16, top: 10 })
    } else {
      Row({ space: 12 }) {
        Image($r('app.media.pic2'))
          .width(64).height(64).borderRadius(32)

        Text('登录/注册')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .onClick(() => {
            // 在这里处理点击事件
            // 1. 模拟登录成功，直接切换状态
            this.isLoggedIn = true;

            // 2. 跳转到登录页
            router.pushUrl({ url: 'pages/login/Login' });
          })
      }
      .width('100%')
      .padding({ left: 16, top: 10 })
      .alignItems(VerticalAlign.Center)
    }
  }

  // --- 用户数据统计构建器 ---
  @Builder
  UserStats() {
    Row() {
      ForEach(this.stats, (item: StatItem) => {
        Column({ space: 8 }) {
          if (item.isIcon) {
            Image(item.icon)
              .width(28).height(28)
          } else {
            Text(item.value)
              .fontSize(20)
              .fontWeight(FontWeight.Bold)
          }
          Text(item.label)
            .fontSize(13)
            .fontColor(Color.Gray)
        }
        .alignItems(HorizontalAlign.Center)
        .layoutWeight(1)
      })
    }
    .width('100%')
    .margin({ top: 24 })

  }

  // --- 卡片头部构建器 ---
  @Builder
  CardHeader(title: string, linkText: string) {
    Row() {
      Text(title)
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
      Blank()
      Row({ space: 2 }) {
        Text(linkText)
          .fontSize(13)
          .fontColor(Color.Gray)
        Image($r('app.media.ic_public_arrow_right'))
          .width(14).height(14)
          .fillColor(Color.Gray)
      }
    }
    .width('100%')
  }

  // --- "我的订单" 卡片构建器 ---
  @Builder
  OrderCard() {
    Column({ space: 20 }) {
      this.CardHeader('我的订单', '全部订单')
      Grid() {
        ForEach(this.orderItems, (item: GridItemType) => {
          GridItem() {
            Column({ space: 8 }) {
              Image(item.icon).width(32).height(32)
              Text(item.label).fontSize(13).fontColor('#333')
            }
          }
        })
        ForEach(this.serviceItems, (item: GridItemType) => {
          GridItem() {
            Column({ space: 8 }) {
              Image(item.icon).width(32).height(32)
              Text(item.label).fontSize(13).fontColor('#333')
            }
          }
        })
      }
      .columnsTemplate('1fr 1fr 1fr 1fr 1fr')
      .rowsGap(16)
      .width('100%')
    }
    .padding(16)
    .backgroundColor(Color.White)
    .borderRadius(12)
  }

  // --- "我的设备" 卡片构建器 ---
  @Builder
  DeviceCard() {
    Column({ space: 12 }) {
      this.CardHeader('我的设备  2台', '全部设备')
      Row({ space: 12 }) {
        Image($r('app.media.phone2')) // 用头像作为设备图片占位符
          .width(80).height(80).objectFit(ImageFit.Contain)
        Column({ space: 6 }) {
          Row({ space: 4 }) {
            Text('vivo X200')
              .fontSize(16)
              .fontWeight(FontWeight.Bold)
            Image($r('app.media.ic_public_arrow_right')) // 用箭头作为外链图标占位符
              .width(14).height(14)
          }
          Text('已陪伴您32天').fontSize(12).fontColor(Color.Gray)
          Text('预计保修至 2026.05.06').fontSize(12).fontColor(Color.Gray)
        }
        .alignItems(HorizontalAlign.Start)
      }
      .width('100%')
      Divider().strokeWidth(0.5).color('#f0f0f0')
      Row() {
        Row({ space: 6 }) {
          Image($r('app.media.setting')) // 用 setting 作为延保图标占位符
            .width(18).height(18)
          Text('延保 (限购机 365 天内购买)')
            .fontSize(13)
        }
        .alignItems(VerticalAlign.Center)
        Blank()
        Button('立即购买')
          .height(32)
          .fontSize(13)
          .borderRadius(16)
      }
      .width('100%')
    }
    .padding(16)
    .backgroundColor(Color.White)
    .borderRadius(12)
  }

  build() {
    Scroll() {
      Column({ space: 16 }) {
        this.TopBar()
        this.UserProfile()
        this.UserStats()
        this.OrderCard()
        this.DeviceCard()
      }
      .width('100%')
      .padding({ bottom: 20 })
    }
    .width('100%')
    .height('100%')
    .scrollBar(BarState.Off)
    .backgroundColor('#f7f8fa')
  }
}

export default UserCenter;