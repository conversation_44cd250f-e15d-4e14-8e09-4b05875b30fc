import promptAction from '@ohos.promptAction';

@Component
export default struct FollowButton {
  @State isFollowed: boolean = false;

  build() {
    Button(this.isFollowed ? '已关注' : '关注')
      .controlSize(ControlSize.SMALL)
      .buttonStyle(ButtonStyleMode.NORMAL)
      .fontColor(this.isFollowed ? '#cf0a2c' : '#000')
      .backgroundColor(this.isFollowed ? '#ffe3e3e3' : '#fff')
      .onClick(() => {
        this.isFollowed = !this.isFollowed;

        try {
          promptAction.showToast({
            message: this.isFollowed ? '已关注' : '已取消关注',
            duration: 2000,
            bottom: 80
          });
        } catch (error) {
          console.error(`showToast error: ${JSON.stringify(error)}`);
        }
      })
  }
}