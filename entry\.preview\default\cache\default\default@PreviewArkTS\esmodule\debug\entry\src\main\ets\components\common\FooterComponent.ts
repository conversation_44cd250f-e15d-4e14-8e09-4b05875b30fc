if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface FooterComponent_Params {
    copyrightYear?: string;
    companyName?: string;
    recordNumber?: string;
}
export class FooterComponent extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.copyrightYear = new Date().getFullYear().toString();
        this.companyName = 'Your Company Name';
        this.recordNumber = '京ICP备XXXXXXXX号-1';
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: FooterComponent_Params) {
        if (params.copyrightYear !== undefined) {
            this.copyrightYear = params.copyrightYear;
        }
        if (params.companyName !== undefined) {
            this.companyName = params.companyName;
        }
        if (params.recordNumber !== undefined) {
            this.recordNumber = params.recordNumber;
        }
    }
    updateStateVars(params: FooterComponent_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
    }
    aboutToBeDeleted() {
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private copyrightYear: string; // 自动获取当前年份
    private companyName: string; // 默认公司名
    private recordNumber: string; // 默认备案号
    // 使用 @Builder 创建一个可复用的链接样式，让代码更整洁
    LinkText(label: string, url: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/components/common/FooterComponent.ets(11:5)", "entry");
            Text.fontSize(12);
            Text.fontColor('#007DFF');
            Text.onClick(() => {
                console.info(`Footer link clicked: ${url}`);
            });
        }, Text);
        Text.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 8 });
            Column.debugLine("entry/src/main/ets/components/common/FooterComponent.ets(20:5)", "entry");
            Column.width('100%');
            Column.backgroundColor('#f7f8fa');
            Column.align(Alignment.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 第一行：可以放置一些服务链接
            Row.create({ space: 20 });
            Row.debugLine("entry/src/main/ets/components/common/FooterComponent.ets(22:7)", "entry");
            // 第一行：可以放置一些服务链接
            Row.justifyContent(FlexAlign.Center);
            // 第一行：可以放置一些服务链接
            Row.padding({ top: 16 });
        }, Row);
        this.LinkText.bind(this)('关于我们', 'https://example.com/about');
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/components/common/FooterComponent.ets(24:9)", "entry");
            Divider.vertical(true);
            Divider.height(12);
            Divider.color('#dcdcdc');
        }, Divider);
        this.LinkText.bind(this)('服务条款', 'https://example.com/terms');
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/components/common/FooterComponent.ets(26:9)", "entry");
            Divider.vertical(true);
            Divider.height(12);
            Divider.color('#dcdcdc');
        }, Divider);
        this.LinkText.bind(this)('隐私政策', 'https://example.com/privacy');
        // 第一行：可以放置一些服务链接
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 第二行：版权信息
            Text.create(`Copyright © ${this.copyrightYear} ${this.companyName}. All Rights Reserved.`);
            Text.debugLine("entry/src/main/ets/components/common/FooterComponent.ets(33:7)", "entry");
            // 第二行：版权信息
            Text.fontSize(12);
            // 第二行：版权信息
            Text.fontColor('#999999');
        }, Text);
        // 第二行：版权信息
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 第三行：备案号信息
            Text.create(this.recordNumber);
            Text.debugLine("entry/src/main/ets/components/common/FooterComponent.ets(38:7)", "entry");
            // 第三行：备案号信息
            Text.fontSize(12);
            // 第三行：备案号信息
            Text.fontColor('#999999');
        }, Text);
        // 第三行：备案号信息
        Text.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
