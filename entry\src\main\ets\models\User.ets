// entry_ability/src/main/ets/models/User.ets
export interface User {
  id?: string; // id 可能在创建时是可选的
  name: string;
  email: string;
  phone?: string; // 手机号
  password?: string; // 密码（注册时使用）
}

// 登录请求接口
export interface LoginRequest {
  phone: string;
  password?: string; // 密码登录时使用
  code?: string; // 验证码登录时使用
}

// 注册请求接口
export interface RegisterRequest {
  name: string;
  phone: string;
  password: string;
  email?: string;
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
}