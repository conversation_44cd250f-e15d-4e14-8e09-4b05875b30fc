{"app": {"bundleName": "com.example.harmonyhelloworld", "vendor": "example", "versionCode": 1000000, "versionName": "1.0.0", "icon": "$media:app_icon", "label": "$string:app_name", "apiReleaseType": "Release", "compileSdkVersion": "*********", "targetAPIVersion": 50002014, "minAPIVersion": 50000012, "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app", "buildMode": "debug", "debug": true, "iconId": 16777217, "labelId": 16777216}, "module": {"requestPermissions": [{"name": "ohos.permission.INTERNET"}], "name": "entry", "type": "entry", "description": "$string:module_desc", "mainElement": "EntryAbility", "deviceTypes": ["phone", "tablet", "2in1"], "deliveryWithInstall": true, "installationFree": false, "pages": "$profile:main_pages", "abilities": [{"name": "EntryAbility", "srcEntry": "./ets/entryability/EntryAbility.ets", "description": "$string:EntryAbility_desc", "icon": "$media:layered_image", "label": "$string:EntryAbility_label", "startWindowIcon": "$media:startIcon", "startWindowBackground": "$color:start_window_background", "exported": true, "skills": [{"entities": ["entity.system.home"], "actions": ["action.system.home"]}], "descriptionId": 16777228, "iconId": 16777243, "labelId": 16777229, "startWindowIconId": 16777264, "startWindowBackgroundId": 16777272, "ohmurl": "@normalized:N&undefined&undefined&undefined/src/main/ets/entryability/EntryAbility&undefined"}], "extensionAbilities": [{"name": "EntryBackupAbility", "srcEntry": "./ets/entrybackupability/EntryBackupAbility.ets", "type": "backup", "exported": false, "metadata": [{"name": "ohos.extension.backup", "resource": "$profile:backup_config", "resourceId": 16777234}]}], "packageName": "entry", "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": [], "descriptionId": 16777230}}