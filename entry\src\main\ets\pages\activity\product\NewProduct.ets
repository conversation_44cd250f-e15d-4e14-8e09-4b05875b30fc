import VideoModule from '../../../components/common/VideoModule'
import FollowButton from '../../../components/common/FollowButton';
@Component
struct NewProduct {
  build() {
    Scroll() {
      Column() {
        Text('优质作者推荐')
        Column() {
          Row() {
            Image($r('app.media.pic1'))
              .width(50)
              .borderRadius(500)
            Text('快乐梦想家小王')
              .fontWeight(600)
            FollowButton()
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceAround)
          .padding({
            top: 15,
            bottom: 15
          })

          Image($r('app.media.pic2'))
            .width('100%')
            .borderRadius({
              topLeft: 15,
              topRight: 15
            })

        }
        .backgroundColor('#fff')
        .width('100%')
        .borderRadius(15)
        .margin({ top: 6, bottom: 6 })

        // 视频
        Column() {
          Row() {
            Text('新起点')
              .fontWeight(600)
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceAround)
          .padding({
            top: 15,
            bottom: 15
          })
          VideoModule()
            .height(180)
        }
        .backgroundColor('#fff')
        .width('100%')
        .height(230)
        .borderRadius(15)
        .clip(true)
        .margin({ top: 6, bottom: 6 })

        Column() {
          Row() {
            Image($r('app.media.pic3'))
              .width(50)
              .borderRadius(500)
            Text('快乐梦想家小王')
              .fontWeight(600)
            FollowButton()
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceAround)
          .padding({
            top: 15,
            bottom: 15
          })
          Image($r('app.media.pic4'))
            .width('100%')
            .borderRadius({
              topLeft: 15,
              topRight: 15
            })

        }
        .backgroundColor('#fff')
        .width('100%')
        .borderRadius(15)
        .margin({ top: 6, bottom: 6 })

        Column() {
          Row() {
            Image($r('app.media.pic1'))
              .width(50)
              .borderRadius(500)
            Text('快乐梦想家小王')
              .fontWeight(600)
            FollowButton()
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceAround)
          .padding({
            top: 15,
            bottom: 15
          })
          Image($r('app.media.pic2'))
            .width('100%')
            .borderRadius({
              topLeft: 15,
              topRight: 15
            })

        }
        .backgroundColor('#fff')
        .width('100%')
        .borderRadius(15)
        .margin({ top: 6, bottom: 6 })
      }
      .width('100%')

      // .height('100%')
    }
    .width('100%')
    .height('100%')
    .scrollBar(BarState.Off)
  }
}

export default NewProduct