@Component
export struct FooterComponent {

  private copyrightYear: string = new Date().getFullYear().toString(); // 自动获取当前年份
  private companyName: string = 'Your Company Name'; // 默认公司名
  private recordNumber: string = '京ICP备XXXXXXXX号-1'; // 默认备案号

  // 使用 @Builder 创建一个可复用的链接样式，让代码更整洁
  @Builder
  LinkText(label: string, url: string) {
    Text(label)
      .fontSize(12)
      .fontColor('#007DFF') // 蓝色，表示可点击
      .onClick(() => {
        console.info(`Footer link clicked: ${url}`);
      })
  }

  build() {
    Column({ space: 8 }) {
      // 第一行：可以放置一些服务链接
      Row({ space: 20 }) {
        this.LinkText('关于我们', 'https://example.com/about')
        Divider().vertical(true).height(12).color('#dcdcdc')
        this.LinkText('服务条款', 'https://example.com/terms')
        Divider().vertical(true).height(12).color('#dcdcdc')
        this.LinkText('隐私政策', 'https://example.com/privacy')
      }
      .justifyContent(FlexAlign.Center)
      .padding({ top: 16 })

      // 第二行：版权信息
      Text(`Copyright © ${this.copyrightYear} ${this.companyName}. All Rights Reserved.`)
        .fontSize(12)
        .fontColor('#999999')

      // 第三行：备案号信息
      Text(this.recordNumber)
        .fontSize(12)
        .fontColor('#999999')
    }
    .width('100%')
    .backgroundColor('#f7f8fa')
    .align(Alignment.Center) // 确保内容居中
  }
}