import { router } from "@kit.ArkUI"

@Component
export struct HomeRecommend {
  build() {
    Column() {

      Column() {
        Text('V秒杀')
          .fontSize(18)
          .fontWeight(700)
        Swiper() {
          ForEach(Array.from({ length: 5 }), () => {
            Column() {
              Image("https://shopstatic.vivo.com.cn/vivoshop/commodity/commodity/10008803_1739265288243_750x750.png.webp")
                .width('100%')
              Text('vivo耳机')
                .fontSize(16)
                .fontWeight(600)
              Text('深度降噪 | 智慧动态降噪| 低音澎湃')
                .fontSize(12)
                .fontColor('#666')
                .margin({
                  top: 5
                })
              Row() {
                Text(`￥9.9起`)
                  .fontSize(13)
                  .fontColor('#000')
                  .fontWeight(600)
                Blank()
                Text('立即购买')
                  .fontSize(13)
                  .fontColor('#000')
                  .fontWeight(600)
                  .backgroundColor('#eee')
                  .borderRadius(20)
                  .padding({
                    left: 20,
                    right: 20,
                    top: 8,
                    bottom: 8
                  })
              }
              .width('100%')
              .margin({
                top: 8
              })
              .padding({ bottom: 20 })
            }
            .alignItems(HorizontalAlign.Start)
            .onClick(() => {
              router.pushUrl({ url: 'pages/product/ProductDetail' })
            })
          })
        }
        .autoPlay(true)
        .indicator(
          Indicator.dot()
            .selectedColor('#cf0a2c')
        )
      }
      .width('100%')
      .backgroundColor('#fff')
      .borderRadius(15)
      .padding({
        left: 15,
        right: 15,
        top: 15
      })
      .alignItems(HorizontalAlign.Start)
    }
    .width('100%')
    .padding({
      left: 12,
      right: 12
    })
    .margin({
      top: 8
    })
  }
}

export default HomeRecommend

