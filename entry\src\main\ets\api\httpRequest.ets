import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from '@ohos/axios'

// 修改baseURL为空，因为我们会提供完整URL
const service = axios.create({
  baseURL: '',
  timeout: 10 * 60 * 1000
})

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 可以在这里添加token等认证信息
    return config
  },
  (error: AxiosError) => {
    return Promise.reject(error.response)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse): any => {
    return response.data
  },
  (error: AxiosError) => {
    return Promise.reject(error.response)
  }
)

interface configType {
  params?: object
}

interface RequestArgumentType {
  url: string,
  method: string,
  data?: object,
  config?: configType
}

interface AxiosConfigType {
  get: (url: string, params: object) => Promise<any>
  post: (url: string, data: object, config: configType) => Promise<any>
  put: (url: string, data: object, config: configType) => Promise<any>
  delete: (url: string, data: object) => Promise<any>
}

// 请求类型配置
const axiosConfig:AxiosConfigType = {
  get: (url: string, params: object) => {
    console.log('2222', service.get(url, { params }))
    return service.get(url, { params })
  },
  post: (url: string, data: object, config: configType) => {
    return service.post(url, data, {
      headers: {
        'Content-Type': 'application/json'
      },
      params: config?.params
    })
  },
  put: (url: string, data: object, config: configType) => {
    return service.put(url, data, {
      params: config?.params
    })
  },
  delete: (url: string, data: object) => {
    return service.delete(url, { params: data })
  }
}

// 请求入口
const httpRequest = (data: RequestArgumentType): Promise<any> => {

  return Object(axiosConfig)[data.method](data.url, data?.data || {}, data?.config || {})
}

export default httpRequest