import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from '@ohos/axios'

// 修改baseURL为空，因为我们会提供完整URL
const service = axios.create({
  baseURL: '',
  timeout: 10 * 60 * 1000
})

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 可以在这里添加token等认证信息
    return config
  },
  (error: AxiosError) => {
    return Promise.reject(error.response)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data
  },
  (error: AxiosError) => {
    return Promise.reject(error.response)
  }
)

interface configType {
  params?: object
}

interface RequestArgumentType {
  url: string,
  method: string,
  data?: object,
  config?: configType
}

interface AxiosConfigType {
  get: <T>(url: string, params: object) => Promise<T>
  post: <T>(url: string, data: object, config: configType) => Promise<T>
  put: <T>(url: string, data: object, config: configType) => Promise<T>
  delete: <T>(url: string, data: object) => Promise<T>
}

// 请求类型配置
const axiosConfig:AxiosConfigType = {
  get: <T>(url: string, params: object): Promise<T> => {
    console.log('2222', service.get(url, { params }))
    return service.get(url, { params })
  },
  post: <T>(url: string, data: object, config: configType): Promise<T> => {
    return service.post(url, data, {
      headers: {
        'Content-Type': 'application/json'
      },
      params: config?.params
    })
  },
  put: <T>(url: string, data: object, config: configType): Promise<T> => {
    return service.put(url, data, {
      params: config?.params
    })
  },
  delete: <T>(url: string, data: object): Promise<T> => {
    return service.delete(url, { params: data })
  }
}

// 请求入口
const httpRequest = <T>(data: RequestArgumentType): Promise<T> => {
  return (Object(axiosConfig)[data.method] as Function)(data.url, data?.data || {}, data?.config || {})
}

export default httpRequest