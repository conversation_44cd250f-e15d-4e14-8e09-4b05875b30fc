{"deviceType": "phone,tablet,2in1", "checkEntry": "true", "localPropertiesPath": "D:\\DevEcoStudio\\DevEcoStudio\\harmonyProject-master\\local.properties", "Path": "D:\\DevEcoStudio\\DevEcoStudio\\tools\\node\\", "note": "false", "aceProfilePath": "D:\\DevEcoStudio\\DevEcoStudio\\harmonyProject-master\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", "isPreview": "true", "hapMode": "false", "buildMode": "debug", "img2bin": "true", "projectProfilePath": "D:\\DevEcoStudio\\DevEcoStudio\\harmonyProject-master\\build-profile.json5", "watchMode": "true", "appResource": "D:\\DevEcoStudio\\DevEcoStudio\\harmonyProject-master\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", "logLevel": "3", "stageRouterConfig": {"contents": ["{\"module\":{\"pages\":\"$profile:main_pages\",\"name\":\"entry\"}}", "{\"src\":[\"pages/Index\",\"pages/login/CountryRegion\",\"pages/login/Login\",\"pages/search/SearchPage\",\"pages/search/SearchShowPage\",\"pages/product/ProductDetail\"]}"], "paths": ["D:\\DevEcoStudio\\DevEcoStudio\\harmonyProject-master\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "D:\\DevEcoStudio\\DevEcoStudio\\harmonyProject-master\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json"]}, "port": "29922", "aceBuildJson": "D:\\DevEcoStudio\\DevEcoStudio\\harmonyProject-master\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", "aceModuleRoot": "D:\\DevEcoStudio\\DevEcoStudio\\harmonyProject-master\\entry\\src\\main\\ets", "aceSoPath": "D:\\DevEcoStudio\\DevEcoStudio\\harmonyProject-master\\entry\\.preview\\cache\\nativeDependencies.txt", "cachePath": "D:\\DevEcoStudio\\DevEcoStudio\\harmonyProject-master\\entry\\.preview\\cache\\.default", "aceModuleBuild": "D:\\DevEcoStudio\\DevEcoStudio\\harmonyProject-master\\entry\\.preview\\default\\intermediates\\assets\\default\\ets", "aceModuleJsonPath": "D:\\DevEcoStudio\\DevEcoStudio\\harmonyProject-master\\entry\\.preview\\default\\intermediates\\res\\default\\module.json"}