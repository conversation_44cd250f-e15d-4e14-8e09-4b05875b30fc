import ClassifyList from './classify/ClassifyList'
import { router } from '@kit.ArkUI'

@Component
struct Classify {
  build() {
    Column() {
      Row({ space: 10 }) {
        Row({ space: 5 }) {
          Image($r('app.media.ic_public_search'))
            .width(16)
            .fillColor('#5c5c5d')
          Text('充电器')
            .fontSize(16)
            .fontColor('#5c5c5d')
            .onClick(() => {
              router.pushUrl({
                url: 'pages/search/SearchPage'
              })
            })
        }
        .backgroundColor('#fff')
        .borderRadius(20)
        .layoutWeight(1)
        .padding(10)

        Image($r('app.media.shopcart'))
          .width(28)
        Image($r('app.media.comments'))
          .width(28)

      }
      .width('100%')
      .height(58)
      .padding({
        left: 10,
        right: 10
      })

      // 列表部分
      ClassifyList()
        .layoutWeight(1)
    }
    .height('100%')
    .backgroundColor('#f1f3f5')
  }
}

export default Classify