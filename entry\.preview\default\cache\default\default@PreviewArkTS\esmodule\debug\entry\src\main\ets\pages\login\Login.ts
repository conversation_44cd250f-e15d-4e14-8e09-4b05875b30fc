if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface Login_Params {
    message?: string;
    isCodeLoginShow?: boolean;
    addressLabel?: string;
    title?: string;
}
import router from "@ohos:router";
interface ParamsType {
    label: string;
}
class Login extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__message = new ObservedPropertySimplePU('登录帐号以使用更多服务', this, "message");
        this.__isCodeLoginShow = new ObservedPropertySimplePU(true, this, "isCodeLoginShow");
        this.__addressLabel = new ObservedPropertySimplePU('中国 +86', this, "addressLabel");
        this.title = 'vivo';
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: Login_Params) {
        if (params.message !== undefined) {
            this.message = params.message;
        }
        if (params.isCodeLoginShow !== undefined) {
            this.isCodeLoginShow = params.isCodeLoginShow;
        }
        if (params.addressLabel !== undefined) {
            this.addressLabel = params.addressLabel;
        }
        if (params.title !== undefined) {
            this.title = params.title;
        }
    }
    updateStateVars(params: Login_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__message.purgeDependencyOnElmtId(rmElmtId);
        this.__isCodeLoginShow.purgeDependencyOnElmtId(rmElmtId);
        this.__addressLabel.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__message.aboutToBeDeleted();
        this.__isCodeLoginShow.aboutToBeDeleted();
        this.__addressLabel.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __message: ObservedPropertySimplePU<string>;
    get message() {
        return this.__message.get();
    }
    set message(newValue: string) {
        this.__message.set(newValue);
    }
    private __isCodeLoginShow: ObservedPropertySimplePU<boolean>;
    get isCodeLoginShow() {
        return this.__isCodeLoginShow.get();
    }
    set isCodeLoginShow(newValue: boolean) {
        this.__isCodeLoginShow.set(newValue);
    }
    private __addressLabel: ObservedPropertySimplePU<string>;
    get addressLabel() {
        return this.__addressLabel.get();
    }
    set addressLabel(newValue: string) {
        this.__addressLabel.set(newValue);
    }
    private title: string;
    onPageShow(): void {
        if (router.getParams() !== undefined) {
            this.addressLabel = (router.getParams() as ParamsType).label;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/login/Login.ets(22:5)", "entry");
            Column.width("100%");
            Column.height('100%');
            Column.padding(20);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777221, "type": 20000, params: [], "bundleName": "com.example.harmonyhelloworld", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/login/Login.ets(23:7)", "entry");
            Image.width(50);
            Image.margin({ top: 30 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.title);
            Text.debugLine("entry/src/main/ets/pages/login/Login.ets(26:7)", "entry");
            Text.fontSize(25);
            Text.fontWeight(900);
            Text.margin({ top: 20, bottom: 5 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.message);
            Text.debugLine("entry/src/main/ets/pages/login/Login.ets(30:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666');
            Text.margin({ bottom: 50 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 短信验证码登录
            if (this.isCodeLoginShow) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/login/Login.ets(36:33)", "entry");
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/login/Login.ets(37:9)", "entry");
                        Row.width('100%');
                        Row.justifyContent(FlexAlign.SpaceBetween);
                        Row.padding({ left: 10, right: 10 });
                        Row.margin({ bottom: 20 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('国家/地区');
                        Text.debugLine("entry/src/main/ets/pages/login/Login.ets(38:11)", "entry");
                        Text.fontColor('#666');
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/login/Login.ets(40:11)", "entry");
                        Row.onClick(() => {
                            router.pushUrl({
                                url: 'pages/login/CountryRegion',
                            });
                        });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.addressLabel);
                        Text.debugLine("entry/src/main/ets/pages/login/Login.ets(41:13)", "entry");
                        Text.fontColor('#666');
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Image.create({ "id": 16777233, "type": 20000, params: [], "bundleName": "com.example.harmonyhelloworld", "moduleName": "entry" });
                        Image.debugLine("entry/src/main/ets/pages/login/Login.ets(43:13)", "entry");
                        Image.width(20);
                        Image.fillColor('#666');
                    }, Image);
                    Row.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        TextInput.create({
                            placeholder: '手机号'
                        });
                        TextInput.debugLine("entry/src/main/ets/pages/login/Login.ets(58:9)", "entry");
                        TextInput.maxLength(20);
                        TextInput.type(InputType.Number);
                    }, TextInput);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create({ space: 10 });
                        Row.debugLine("entry/src/main/ets/pages/login/Login.ets(63:9)", "entry");
                        Row.margin({ top: 20, bottom: 30 });
                        Row.width('100%');
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        TextInput.create({
                            placeholder: '短信验证码'
                        });
                        TextInput.debugLine("entry/src/main/ets/pages/login/Login.ets(64:11)", "entry");
                        TextInput.width('60%');
                        TextInput.type(InputType.Number);
                    }, TextInput);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('获取验证码');
                        Button.debugLine("entry/src/main/ets/pages/login/Login.ets(69:11)", "entry");
                        Button.buttonStyle(ButtonStyleMode.NORMAL);
                        Button.onClick(() => {
                        });
                    }, Button);
                    Button.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('登录/注册');
                        Button.debugLine("entry/src/main/ets/pages/login/Login.ets(77:9)", "entry");
                        Button.width('100%');
                        Button.onClick(() => {
                        });
                        Button.margin({ bottom: 15 });
                    }, Button);
                    Button.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('密码登录');
                        Text.debugLine("entry/src/main/ets/pages/login/Login.ets(82:9)", "entry");
                        Text.fontColor('#007dff');
                        Text.onClick(() => {
                            this.isCodeLoginShow = false;
                        });
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 密码登录
            if (!this.isCodeLoginShow) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/login/Login.ets(89:34)", "entry");
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        TextInput.create({
                            placeholder: '手机号'
                        });
                        TextInput.debugLine("entry/src/main/ets/pages/login/Login.ets(90:9)", "entry");
                        TextInput.maxLength(20);
                        TextInput.type(InputType.Number);
                    }, TextInput);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        TextInput.create({
                            placeholder: '密码'
                        });
                        TextInput.debugLine("entry/src/main/ets/pages/login/Login.ets(95:9)", "entry");
                        TextInput.type(InputType.Password);
                        TextInput.margin({ top: 20, bottom: 30 });
                    }, TextInput);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/login/Login.ets(100:9)", "entry");
                        Row.width('100%');
                        Row.justifyContent(FlexAlign.SpaceBetween);
                        Row.padding({ left: 10, right: 10 });
                        Row.margin({ bottom: 25 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('短信验证码登录');
                        Text.debugLine("entry/src/main/ets/pages/login/Login.ets(101:11)", "entry");
                        Text.fontColor('#007dff');
                        Text.onClick(() => {
                            this.isCodeLoginShow = true;
                        });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/login/Login.ets(106:11)", "entry");
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('忘记密码');
                        Text.debugLine("entry/src/main/ets/pages/login/Login.ets(107:13)", "entry");
                        Text.fontColor('#007dff');
                    }, Text);
                    Text.pop();
                    Row.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('登录');
                        Button.debugLine("entry/src/main/ets/pages/login/Login.ets(116:9)", "entry");
                        Button.width('100%');
                        Button.onClick(() => {
                        });
                        Button.margin({ bottom: 15 });
                    }, Button);
                    Button.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('注册账号');
                        Button.debugLine("entry/src/main/ets/pages/login/Login.ets(121:9)", "entry");
                        Button.width('100%');
                        Button.buttonStyle(ButtonStyleMode.NORMAL);
                        Button.onClick(() => {
                        });
                        Button.margin({ bottom: 15 });
                    }, Button);
                    Button.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/login/Login.ets(129:7)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('其他方式登录');
            Text.debugLine("entry/src/main/ets/pages/login/Login.ets(131:7)", "entry");
            Text.fontColor('#666');
            Text.fontSize(14);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777232, "type": 20000, params: [], "bundleName": "com.example.harmonyhelloworld", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/login/Login.ets(134:7)", "entry");
            Image.width(40);
            Image.border({
                width: 1,
                color: '#ccc'
            });
            Image.padding(10);
            Image.borderRadius(20);
            Image.margin({ top: 20, bottom: 20 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/login/Login.ets(143:7)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('遇到问题');
            Text.debugLine("entry/src/main/ets/pages/login/Login.ets(144:9)", "entry");
            Text.fontColor('#007dff');
            Text.fontSize(14);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/login/Login.ets(145:9)", "entry");
            Divider.vertical(true);
            Divider.height(20);
            Divider.width(2);
            Divider.color('#666');
            Divider.margin({ left: 20, right: 20 });
        }, Divider);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('用户协议');
            Text.debugLine("entry/src/main/ets/pages/login/Login.ets(151:9)", "entry");
            Text.fontColor('#007dff');
            Text.fontSize(14);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/login/Login.ets(152:9)", "entry");
            Divider.vertical(true);
            Divider.height(20);
            Divider.width(2);
            Divider.color('#666');
            Divider.margin({ left: 20, right: 20 });
        }, Divider);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('隐私声明');
            Text.debugLine("entry/src/main/ets/pages/login/Login.ets(158:9)", "entry");
            Text.fontColor('#007dff');
            Text.fontSize(14);
        }, Text);
        Text.pop();
        Row.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "Login";
    }
}
registerNamedRoute(() => new Login(undefined, {}), "", { bundleName: "com.example.harmonyhelloworld", moduleName: "entry", pagePath: "pages/login/Login", pageFullPath: "entry/src/main/ets/pages/login/Login", integratedHsp: "false", moduleType: "followWithHap" });
