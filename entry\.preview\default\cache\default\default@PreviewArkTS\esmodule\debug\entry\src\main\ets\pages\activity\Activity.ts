if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface Activity_Params {
    bars?: TabType[];
    currentIndex?: number;
}
import NewProduct from "@bundle:com.example.harmonyhelloworld/entry/ets/pages/activity/product/NewProduct";
interface TabType {
    title: string;
}
class Activity extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__bars = new ObservedPropertyObjectPU([
            { title: '关注' },
            { title: '推荐' },
            { title: '圈子' },
            { title: '推荐' },
        ], this, "bars");
        this.__currentIndex = new ObservedPropertySimplePU(0, this, "currentIndex");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: Activity_Params) {
        if (params.bars !== undefined) {
            this.bars = params.bars;
        }
        if (params.currentIndex !== undefined) {
            this.currentIndex = params.currentIndex;
        }
    }
    updateStateVars(params: Activity_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__bars.purgeDependencyOnElmtId(rmElmtId);
        this.__currentIndex.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__bars.aboutToBeDeleted();
        this.__currentIndex.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __bars: ObservedPropertyObjectPU<TabType[]>;
    get bars() {
        return this.__bars.get();
    }
    set bars(newValue: TabType[]) {
        this.__bars.set(newValue);
    }
    private __currentIndex: ObservedPropertySimplePU<number>;
    get currentIndex() {
        return this.__currentIndex.get();
    }
    set currentIndex(newValue: number) {
        this.__currentIndex.set(newValue);
    }
    TabBarBuilder(item: TabType, index: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/activity/Activity.ets(19:5)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.title);
            Text.debugLine("entry/src/main/ets/pages/activity/Activity.ets(20:7)", "entry");
            Text.fontColor(index === this.currentIndex ? '#000' : '#666');
            Text.fontSize(15);
            Text.fontWeight(600);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/activity/Activity.ets(24:7)", "entry");
            Row.width(15);
            Row.height(2);
            Row.backgroundColor(index === this.currentIndex ? '#000' : Color.Transparent);
            Row.margin({
                top: 8
            });
        }, Row);
        Row.pop();
        Column.pop();
    }
    // 加载菜单对应的组件
    TabEachBuilder(index: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (index === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new NewProduct(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/activity/Activity.ets", line: 39, col: 7 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {};
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "NewProduct" });
                    }
                });
            }
            else if (index === 1) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(index.toString());
                        Text.debugLine("entry/src/main/ets/pages/activity/Activity.ets(41:7)", "entry");
                    }, Text);
                    Text.pop();
                });
            }
            else if (index === 2) {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(index.toString());
                        Text.debugLine("entry/src/main/ets/pages/activity/Activity.ets(43:7)", "entry");
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(3, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(index.toString());
                        Text.debugLine("entry/src/main/ets/pages/activity/Activity.ets(45:7)", "entry");
                    }, Text);
                    Text.pop();
                });
            }
        }, If);
        If.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Tabs.create({ index: { value: this.currentIndex, changeEvent: newValue => { this.currentIndex = newValue; } } });
            Tabs.debugLine("entry/src/main/ets/pages/activity/Activity.ets(51:5)", "entry");
            Tabs.animationDuration(0);
            Tabs.backgroundColor('#f1f3f5');
        }, Tabs);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const item = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    TabContent.create(() => {
                        this.TabEachBuilder.bind(this)(index);
                    });
                    TabContent.tabBar({ builder: () => {
                            this.TabBarBuilder.call(this, item, index);
                        } });
                    TabContent.padding({ left: 15, right: 15 });
                    TabContent.debugLine("entry/src/main/ets/pages/activity/Activity.ets(53:9)", "entry");
                }, TabContent);
                TabContent.pop();
            };
            this.forEachUpdateFunction(elmtId, this.bars, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        Tabs.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
export default Activity;
