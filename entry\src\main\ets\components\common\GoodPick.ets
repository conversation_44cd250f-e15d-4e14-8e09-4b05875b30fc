@Component
struct GoodPick {
  build() {
    Column() {
      Text('好物精选')
        .fontSize(16)
        .fontWeight(600)
        .width('100%')
        .margin({
          bottom: 12
        })
      Flex({
        wrap: FlexWrap.Wrap,
        justifyContent: FlexAlign.SpaceBetween
      }) {
        ForEach(Array.from({ length: 20 }), () => {
          Column() {
            Image('https://shopstatic.vivo.com.cn/vivoshop/commodity/42/10009242_1702438015503_750x750.png.webp')
              .width(120)
            Column() {
              Text('vivo TWS 3e 真无线降噪耳机 墨蓝')
                .fontSize(14)
              Text('沉浸环绕音质 | 44h 超长续航 | 智能主动降噪（温馨提示：本产品标配不含充电线）')
                .fontSize(12)
                .fontColor('#666')
                .margin({ top: 6, bottom: 6 })
                .textOverflow({
                  overflow: TextOverflow.Ellipsis
                })
                .maxLines(1)

              Text('￥4599')
                .fontSize(14)
                .fontWeight(600)
            }
            .alignItems(HorizontalAlign.Start)
          }
          .width('49%')
          .backgroundColor('#fff')
          .margin({ bottom: 6 })
          .borderRadius(12)
          .padding(8)
        })
      }
    }
    .margin({
      top: 20,
      bottom: 20
    })
  }
}

export default GoodPick